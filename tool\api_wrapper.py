#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
外部API调用包装模块，为各种API调用添加超时机制和错误处理
"""
import time
from utils.timeout_utils import timeout
from utils.logger import setup_logger
from tool.tencent_api import get_ai_model_query_results
from tool.google_api import get_google_api
from tool.weixin_crawler import get_weixin_articls
from tool.bjx_crawler import get_bjx_news

logger = setup_logger('tool.api_wrapper')

# 各API超时时间配置（秒）
WEIXIN_TIMEOUT = 60
BJX_TIMEOUT = 60
GOOGLE_API_TIMEOUT = 60
AI_MODEL_TIMEOUT = 60

@timeout(WEIXIN_TIMEOUT, "微信公众号文章获取超时")
def get_weixin_articles_with_timeout(wx_name, filter_time, quantity):
    """
    添加超时机制的微信文章获取函数
    
    Args:
        wx_name: 微信公众号名称
        filter_time: 时间过滤条件
        quantity: 需要获取的文章数量
        
    Returns:
        List[Dict]: 文章列表
        
    Raises:
        TimeoutError: 当API调用超时时
    """
    start_time = time.time()
    try:
        result = get_weixin_articls(wx_name, filter_time, quantity)
        elapsed_time = time.time() - start_time
        logger.info(f"获取微信公众号 '{wx_name}' 文章耗时: {elapsed_time:.2f}秒")
        return result
    except Exception as e:
        elapsed_time = time.time() - start_time
        logger.error(f"获取微信公众号 '{wx_name}' 文章出错，耗时: {elapsed_time:.2f}秒，错误: {str(e)}")
        raise

@timeout(BJX_TIMEOUT, "北极星新闻获取超时")
def get_bjx_news_with_timeout(category, keyword, filter_time, quantity):
    """
    添加超时机制的北极星新闻获取函数
    
    Args:
        category: 新闻类别
        keyword: 搜索关键词
        filter_time: 时间过滤条件
        quantity: 需要获取的文章数量
        
    Returns:
        List[Dict]: 文章列表
        
    Raises:
        TimeoutError: 当API调用超时时
    """
    start_time = time.time()
    try:
        result = get_bjx_news(category, keyword, filter_time, quantity)
        elapsed_time = time.time() - start_time
        logger.info(f"获取北极星新闻耗时: {elapsed_time:.2f}秒")
        return result
    except Exception as e:
        elapsed_time = time.time() - start_time
        logger.error(f"获取北极星新闻出错，耗时: {elapsed_time:.2f}秒，错误: {str(e)}")
        raise

@timeout(GOOGLE_API_TIMEOUT, "谷歌API搜索超时")
def get_google_api_with_timeout(keyword, filter_time, quantity):
    """
    添加超时机制的谷歌API搜索函数
    
    Args:
        keyword: 搜索关键词
        filter_time: 时间过滤条件
        quantity: 需要获取的文章数量
        
    Returns:
        List[Dict]: 文章列表
        
    Raises:
        TimeoutError: 当API调用超时时
    """
    start_time = time.time()
    try:
        result = get_google_api(keyword, filter_time, quantity)
        elapsed_time = time.time() - start_time
        logger.info(f"谷歌API搜索耗时: {elapsed_time:.2f}秒")
        return result
    except Exception as e:
        elapsed_time = time.time() - start_time
        logger.error(f"谷歌API搜索出错，耗时: {elapsed_time:.2f}秒，错误: {str(e)}")
        raise

@timeout(AI_MODEL_TIMEOUT, "AI模型搜索超时")
def get_ai_model_query_results_with_timeout(keyword, filter_time, quantity):
    """
    添加超时机制的AI模型搜索函数
    
    Args:
        keyword: 搜索关键词
        filter_time: 时间过滤条件
        quantity: 需要获取的文章数量
        
    Returns:
        List[Dict]: 文章列表
        
    Raises:
        TimeoutError: 当API调用超时时
    """
    start_time = time.time()
    try:
        result = get_ai_model_query_results(keyword, filter_time, quantity)
        elapsed_time = time.time() - start_time
        logger.info(f"AI模型搜索耗时: {elapsed_time:.2f}秒")
        return result
    except Exception as e:
        elapsed_time = time.time() - start_time
        logger.error(f"AI模型搜索出错，耗时: {elapsed_time:.2f}秒，错误: {str(e)}")
        raise 