from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
import json
import re
import time

def get_fingerprint_and_token(driver):
	"""从网络请求中获取 fingerprint 和 token"""
	logs = driver.get_log('performance')
	for entry in logs:
		log = json.loads(entry['message'])['message']
		method = log.get('method')
		
		# 从请求或响应中提取参数
		url = log['params'].get('request', {}).get('url') if method == 'Network.requestWillBeSent' else \
			  log['params'].get('response', {}).get('url') if method == 'Network.responseReceived' else None
		
		if not url:
			continue
			
		# 提取参数
		fingerprint_match = re.search(r"fingerprint=([a-fA-F0-9]+)", url)
		token_match = re.search(r"token=(\d+)", url)
		
		if fingerprint_match and token_match:
			return fingerprint_match.group(1), token_match.group(1)
	
	return None, None

def init_driver():
	"""初始化 Chrome 驱动"""
	chrome_options = Options()
	# chrome_options.add_argument('--headless')
	chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
	chrome_options.add_argument(f"--user-data-dir=D:/chrome_user_data")
	chrome_options.add_argument('--no-sandbox')
	chrome_options.add_argument('--disable-dev-shm-usage')
	chrome_options.add_argument('--disable-gpu')
	chrome_options.add_argument('--disable-extensions')
	chrome_options.add_argument('--disable-software-rasterizer')

	# 使用 ChromeDriver 而不是 Chrome 浏览器
	service = Service("D:/anaconda3/chromedriver.exe")  # 确保chromedriver.exe在当前目录下
	return webdriver.Chrome(service=service, options=chrome_options)

def main():
	driver = init_driver()
	try:
		# 访问首页并等待加载
		driver.get('https://mp.weixin.qq.com/cgi-bin/home')
		time.sleep(5)
		
		# 尝试点击登录按钮
		try:
			jump_link = driver.find_element(By.ID, "jumpUrl")
			jump_link.click()
			time.sleep(3)
		except:
			driver.get('https://mp.weixin.qq.com/')
			time.sleep(30)
		
		# 获取 cookies
		cookies = driver.get_cookies()
		cookie_string = '; '.join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])
		
		# 获取 fingerprint 和 token
		fingerprint, token = get_fingerprint_and_token(driver)
		
		# 输出结果
		print(f"cookie = '{cookie_string}'")
		print(f"token = '{token}'")
		print(f"fingerprint = '{fingerprint}'")
		
	finally:
		driver.quit()

if __name__ == "__main__":
	main()