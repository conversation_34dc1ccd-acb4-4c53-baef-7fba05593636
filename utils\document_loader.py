import os
import chardet
from typing import List, Dict, Any, Optional

from langchain_community.document_loaders import (
    TextLoader, 
    UnstructuredMarkdownLoader, 
    PyMuPDFLoader,
    Docx2txtLoader,
    UnstructuredWordDocumentLoader,
    UnstructuredPowerPointLoader,
    UnstructuredExcelLoader,
    UnstructuredHTMLLoader,
    CSVLoader
)
from langchain_core.documents import Document
from rapidocr_onnxruntime import RapidOCR

def detect_encoding(file_path: str) -> str:
    """
    检测文件编码
    
    Args:
        file_path: 文件路径
        
    Returns:
        检测到的编码，如果无法检测则返回'utf-8'
    """
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read(4096)  # 读取文件前4096字节用于检测
            result = chardet.detect(raw_data)
            encoding = result['encoding']
            confidence = result['confidence']
            
            # 如果检测置信度较低或无法检测，使用默认编码
            if not encoding or confidence < 0.6:
                return 'utf-8'
            
            return encoding
    except Exception:
        # 如果出现任何异常，返回默认编码
        return 'utf-8'

def load_document(file_path: str) -> List[Document]:
    """
    根据文件类型自动选择合适的加载器来加载文档内容。
    
    Args:
        file_path: 文档文件路径
        
    Returns:
        包含文档内容的Document对象列表
        
    Raises:
        ValueError: 如果文件类型不支持或文件不存在
        Exception: 加载过程中发生的其他错误
    """
    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise ValueError(f"文件不存在: {file_path}")
        
    # 获取文件扩展名
    ext = os.path.splitext(file_path)[1].lower()
    
    try:
        # 文本文件 - 需要处理编码问题
        if ext == '.txt':
            # 检测文件编码
            encoding = detect_encoding(file_path)
            # 读取txt文件，使用检测到的编码
            try:
                loader = TextLoader(file_path, encoding=encoding)
                return loader.load()
            except UnicodeDecodeError:
                # 如果仍然出现编码错误，尝试其他常见编码
                for enc in ['utf-8', 'utf-16', 'gbk', 'gb2312', 'latin-1', 'big5']:
                    if enc != encoding:
                        try:
                            loader = TextLoader(file_path, encoding=enc)
                            return loader.load()
                        except UnicodeDecodeError:
                            continue
                # 如果所有编码都失败，使用latin-1（几乎可以解码所有文件，但可能有乱码）
                loader = TextLoader(file_path, encoding='latin-1')
                return loader.load()
        
        # Microsoft Office 文档
        elif ext == '.docx':
            # 读取docx文件
            loader = Docx2txtLoader(file_path)
            return loader.load()
            
        elif ext == '.doc':
            # 读取doc文件
            loader = UnstructuredWordDocumentLoader(file_path)
            return loader.load()
        
        elif ext in ['.ppt', '.pptx']:
            # 读取PowerPoint文件
            loader = UnstructuredPowerPointLoader(file_path)
            return loader.load()
            
        elif ext in ['.xls', '.xlsx']:
            # 读取Excel文件
            loader = UnstructuredExcelLoader(file_path)
            return loader.load()
        
        # PDF文件
        elif ext == '.pdf':
            # 读取pdf文件
            loader = PyMuPDFLoader(file_path, mode="page")
            return loader.load()
        
        # 图片文件
        elif ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif']:
            # 读取图片文件并使用OCR识别
            ocr = RapidOCR()
            result, _ = ocr(file_path)
            if result:
                content = "\n".join([line[1] for line in result])
                return [Document(page_content=content)]
            return []
        
        # 网页和标记语言文件
        elif ext == '.md':
            # 检测编码
            encoding = detect_encoding(file_path)
            # 读取markdown文件
            try:
                # UnstructuredMarkdownLoader 不支持直接指定编码，使用自定义方法
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                return [Document(page_content=content)]
            except UnicodeDecodeError:
                # 如果失败，使用latin-1
                with open(file_path, 'r', encoding='latin-1') as f:
                    content = f.read()
                return [Document(page_content=content)]
            
        elif ext in ['.html', '.htm']:
            # 检测编码
            encoding = detect_encoding(file_path)
            try:
                # 读取HTML文件
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    
                # 使用BeautifulSoup解析内容
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(content, "html.parser")
                text = soup.get_text(separator=" ", strip=True)
                return [Document(page_content=text)]
            except (UnicodeDecodeError, ImportError):
                # 回退到原始加载器
                loader = UnstructuredHTMLLoader(file_path)
                return loader.load()
        
        # 数据文件
        elif ext == '.csv':
            # 检测编码
            encoding = detect_encoding(file_path)
            # 读取CSV文件
            try:
                loader = CSVLoader(file_path, encoding=encoding)
                return loader.load()
            except UnicodeDecodeError:
                # 如果失败，使用latin-1
                loader = CSVLoader(file_path, encoding='latin-1')
                return loader.load()
        
        else:
            # 不支持的文件类型
            raise ValueError(f"不支持的文件类型: {ext}")
            
    except Exception as e:
        raise Exception(f"加载文件时出错: {str(e)}")

def read_file_content(file_path: str) -> Dict[str, Any]:
    """
    读取单个文件并返回其内容和字数统计
    
    Args:
        file_path: 文档文件路径
        
    Returns:
        包含文件内容和统计信息的字典，包括：
        - content: 文件内容文本
        - word_count: 词数
        - file_name: 文件名
        - file_type: 文件类型
        
    Raises:
        ValueError: 如果文件类型不支持、文件不存在或提取内容为空
        Exception: 加载过程中发生的其他错误
    """
    # 获取文件名和类型
    file_name = os.path.basename(file_path)
    file_type = os.path.splitext(file_path)[1].lower()
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise ValueError(f"文件不存在: {file_path}")
    
    # 加载文档
    docs = load_document(file_path)
    
    # 检查是否成功提取内容
    if not docs:
        raise ValueError(f"未能从文件中提取内容: {file_path}")

    # 合并所有文档内容（如PDF可能有多页）
    all_content = "\n\n".join([doc.page_content for doc in docs])
    
    # 检查提取的内容是否为空
    if not all_content.strip():
        raise ValueError(f"提取的文件内容为空: {file_path}")

    # 计算字数统计
    word_count = len(all_content.split())

    return {
        "file_name": file_name,
        "file_type": file_type.lstrip('.'),  # 移除前导点号
        "content": all_content,
        "word_count": word_count,
        "success": True
    }

# 简单测试函数
if __name__ == "__main__":
    # 测试文件路径，请替换为实际文件路径
    test_file = 'C://Users//zhoujiang//Desktop//document_loader.csv'
    
    if os.path.exists(test_file):
        result = read_file_content(test_file)
        
        print(f"文件: {result['file_name']}")
        print(f"类型: {result['file_type']}")
        print(f"状态: {'成功' if result['success'] else '失败'}")
        
        if result['success']:
            print(f"词数: {result['word_count']}")
            print(f"内容预览: {result['content'][:100]}...")
        else:
            print(f"错误信息: {result['message']}")
    else:
        print(f"测试文件不存在: {test_file}")