# AIOS API 项目

## 🚀 项目背景与核心功能

AIOS API 是一个基于 FastAPI 框架构建的智能体服务后端。它提供了一系列接口，用于管理智能体（Agent）、节点（Node）、搜索和资源。通过这些接口，用户可以：

- **用户认证**：实现用户登录、登出，并通过 JWT 进行身份验证。
- **智能体管理**：创建、更新、查询和删除智能体。
- **节点管理**：创建、更新、查询和删除节点，节点可以包含文本内容、搜索配置或关联的知识库资源。
- **内容搜索**：根据关键词、来源类型（微信、网站、国内外搜索）等条件进行文章搜索，并保存搜索结果。
- **知识库资源管理**：上传 URL 或文件作为知识库资源，并关联到特定节点。
- **智能体与节点运行**：运行智能体以生成内容，运行节点以生成总结内容。

## 📂 目录结构

```
. # 项目根目录
├── app.py                      # FastAPI 应用主文件，负责应用初始化、路由注册和中间件配置
├── config.py                   # 应用程序配置，如数据库连接、密钥等
├── models.py                   # 数据库模型定义，使用 SQLAlchemy ORM
├── requirements.txt            # 项目依赖包列表
├── auth/                       # 认证相关模块
│   ├── __init__.py
│   ├── jwt_utils.py            # JWT token 的生成与验证工具
│   └── routes.py               # 认证相关的 API 路由（登录、登出）
├── agents/                     # 智能体相关模块
│   ├── __init__.py
│   ├── routes.py               # 智能体相关的 API 路由（创建、查询、删除、运行）
│   └── services.py             # 智能体业务逻辑实现
├── nodes/                      # 节点相关模块
│   ├── __init__.py
│   ├── routes.py               # 节点相关的 API 路由（创建、查询、删除、运行）
│   └── services.py             # 节点业务逻辑实现
├── search/                     # 搜索相关模块
│   ├── __init__.py
│   ├── routes.py               # 搜索相关的 API 路由
│   └── services.py             # 搜索业务逻辑实现，调用外部爬虫和API
├── resources/                  # 资源管理模块（知识库）
│   ├── __init__.py
│   ├── routes.py               # 资源相关的 API 路由（URL上传、文件上传、删除）
│   └── services.py             # 资源业务逻辑实现
├── tool/                       # 外部工具和爬虫模块
│   ├── __init__.py
│   ├── api_wrapper.py          # 外部 API 调用的封装，添加超时机制
│   ├── bjx_crawler.py          # 北极星新闻爬虫
│   ├── google_api.py           # 谷歌搜索 API 封装
│   ├── save_weixin_config.py   # 微信配置保存工具
│   ├── tencent_api.py          # 腾讯 AI 模型 API 封装
│   ├── weixin_crawler.py       # 微信公众号文章爬虫
│   └── weixin_selenium.py      # 微信 Selenium 辅助工具
└── utils/                      # 工具函数和通用模块
    ├── __init__.py
    ├── date_utils.py           # 日期处理工具
    ├── document_loader.py      # 文档加载工具
    ├── error_handler.py        # 错误处理工具
    ├── logger.py               # 日志配置
    ├── timeout_utils.py        # 超时处理工具
    └── url_processor.py        # URL 内容处理工具
```

## 🚀 使用方法

### 1. 环境准备

确保您的系统安装了 Python 3.8+ 版本。

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置数据库

在 `config.py` 文件中配置您的 MySQL 数据库连接信息和 JWT `SECRET_KEY`。例如：

```python
# config.py
class Config:
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://root:password@localhost:3306/aios_db'
    SECRET_KEY = 'your_secret_key_for_jwt'
    REDIS_HOST = 'localhost'
    REDIS_PORT = 6379
    REDIS_DB = 0
    REDIS_PASSWORD = None # 如果没有密码，设置为 None
    WEIXIN_CONFIG_KEY = 'weixin_crawler_config'
```

### 4. 运行应用

```bash
python app.py
```

应用将会在 `http://0.0.0.0:5000` 上运行。您可以通过访问 `http://0.0.0.0:5000/docs` 查看 Swagger UI 接口文档。

## 🛠️ 接口参数说明与返回格式

所有 API 接口都遵循以下通用返回格式：

```json
{
  "code": 200,      // 状态码，200 表示成功，其他表示错误
  "message": "操作成功", // 接口操作结果的描述信息
  "data": {}        // 返回的具体数据，不同接口返回的数据结构不同
}
```

当发生错误时，`code` 通常会是 HTTP 状态码（如 400, 401, 404, 500），`message` 会包含具体的错误描述。

详细接口文档请参考运行后的 Swagger UI：`http://0.0.0.0:5000/docs`

## 📈 改进建议与后续规划

1.  **数据库初始化与迁移**：目前数据库表依赖于手动创建。未来可以考虑引入 Alembic 等工具，实现数据库迁移管理，方便开发和部署。
2.  **更完善的错误处理**：虽然已集成自定义错误处理，但可以进一步细化不同类型的 `HTTPException` 和自定义 `APIError`，提供更具体的错误码和更友好的错误信息。
3.  **日志系统优化**：目前的日志系统是简单的文件记录。可以考虑集成更专业的日志库（如 `loguru`），支持日志轮转、分级、结构化日志输出等功能，方便日志分析和问题排查。
4.  **安全增强**：虽然使用了 JWT 认证，但生产环境可以考虑增加更多的安全措施，例如：
    *   **HTTPS**：部署时强制使用 HTTPS，保护数据传输安全。
    *   **速率限制**：对 API 请求进行速率限制，防止恶意攻击或滥用。
    *   **输入校验加强**：虽然 Pydantic 提供了强大的校验功能，但可以针对敏感或复杂字段增加更细致的业务逻辑校验。
5.  **异步数据库操作**：当前 SQLAlchemy 的数据库操作仍然是同步的。为了充分利用 FastAPI 的异步特性，可以考虑引入 `SQLAlchemy-Utils` 或 `asyncpg` 等异步数据库驱动，实现真正的异步数据库操作，提高并发性能。
6.  **配置文件管理**：将 `config.py` 中的敏感信息（如 `SECRET_KEY` 和数据库凭证）从代码中分离，使用环境变量或专门的配置管理工具（如 `python-decouple`、`Dynaconf`）进行管理，提高安全性。
7.  **模块解耦与扩展性**：随着项目规模的扩大，可以考虑进一步解耦服务层逻辑，引入依赖注入容器（如 `FastAPI-Inject`），使代码更模块化、可测试和可扩展。
8.  **外部服务高可用**：对于微信、谷歌等外部爬虫或 API 调用，可以考虑增加重试机制、熔断机制和降级策略，提高服务的健壮性。
9.  **单元测试**：尽管用户不喜欢测试代码，但为了项目的长期稳定性和可维护性，建议逐步引入单元测试和集成测试，确保每个模块和接口的正确性。
10. **部署自动化**：编写 Dockerfile 和 docker-compose 文件，实现应用的容器化部署，简化部署流程。

## 许可证

(根据项目实际情况添加许可证信息，例如 MIT License) 