from fastapi import APIRouter, Request, Depends, HTTPException, status
from sqlalchemy.orm import Session
from models import User, get_db
from auth.jwt_utils import jwt_manager
from utils.logger import setup_logger
from utils.error_handler import APIError
from datetime import datetime
from pydantic import BaseModel

logger = setup_logger('auth.routes')

auth_router = APIRouter(prefix="/api", tags=["auth"])

class UserLogin(BaseModel):
    username: str
    password: str

@auth_router.post("/login")
async def login(user_login: UserLogin, request: Request, db: Session = Depends(get_db)):
    """
    用户登录接口
    """
    try:
        username = user_login.username
        password = user_login.password
        
        user = db.query(User).filter(User.username == username).first()
        if not user or not user.check_password(password):
            logger.warning(f"登录失败，用户名或密码错误: username={username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
            
        # 更新用户登录信息
        user.last_login = datetime.now()
        user.last_login_ip = request.client.host if request.client else None
        db.commit()
        db.refresh(user)
        
        # 生成token
        token = jwt_manager.generate_token(user.id, user.username)
        
        logger.info(f"用户登录成功: user_id={user.id}, username={username}")
        return {
            "code": 200,
            "message": "登录成功",
            "data": {
                'token': token,
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'last_login': user.last_login.isoformat() if user.last_login else None,
                }
            }
        }
    except APIError as e:
        logger.error(f"登录失败: {str(e)}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"登录时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )

@auth_router.post("/logout")
async def logout(current_user: dict = Depends(jwt_manager.get_current_user)):
    """
    用户登出接口
    
    注意: 由于使用JWT认证，服务端不需要实际处理登出操作，
    客户端只需要移除本地存储的token即可。
    """
    try:
        user_id = current_user['user_id']
        username = current_user['username']
        logger.info(f"用户登出: user_id={user_id}, username={username}")
        return {"code": 200, "message": "登出成功", "data": {}}
    except Exception as e:
        logger.error(f"登出时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登出失败: {str(e)}"
        )