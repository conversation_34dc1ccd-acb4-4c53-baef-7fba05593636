from fastapi import APIRouter, Request, Depends, HTTPException, status
from sqlalchemy.orm import Session
from auth.jwt_utils import jwt_manager
# from utils.validators import validate_json
from utils.logger import setup_logger
# from utils.response import success, bad_request, internal_error
from search.services import search_articles
from models import get_db
from pydantic import BaseModel, Field
from typing import Optional

logger = setup_logger('search.routes')

search_router = APIRouter(prefix="/api", tags=["search"])

class SearchRequest(BaseModel):
    search_query: str
    source_type: str
    filter_quantity: int
    source_list: Optional[str] = None
    filter_time: Optional[str] = None
    temp_node_id: Optional[str] = None
    node_id: Optional[int] = None

    # Custom validation to ensure at least one of temp_node_id or node_id is provided
    def model_post_init(self, __context: any) -> None:
        if not self.temp_node_id and not self.node_id:
            raise ValueError("必须提供临时节点ID(temp_node_id)或正式节点ID(node_id)中的至少一个")

@search_router.post("/search")
async def handle_search(
    search_data: SearchRequest,
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    搜索内容接口
    """
    try:
        user_id = current_user['user_id']
        result_list = search_articles(search_data.model_dump(exclude_unset=True), user_id, db)
        return {"code": 200, "message": "搜索成功", "data": result_list}
        
    except ValueError as e:
        logger.warning(f"搜索请求验证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"搜索时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索失败: {str(e)}"
        ) 