import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import re

# 下拉列表选择
def convert_relative_time(time_str):
    """
    将相对时间转换为具体日期
    例如：'23分钟前' -> '2025-04-21'
    """
    now = datetime.now()
    
    # 如果已经是yyyy-mm-dd格式，直接返回
    if re.match(r'\d{4}-\d{2}-\d{2}', time_str):
        return time_str
        
    # 解析相对时间
    if '分钟前' in time_str:
        minutes = int(time_str.replace('分钟前', ''))
        date = now - timedelta(minutes=minutes)
    elif '小时前' in time_str:
        hours = int(time_str.replace('小时前', ''))
        date = now - timedelta(hours=hours)
    elif '天前' in time_str:
        days = int(time_str.replace('天前', ''))
        date = now - timedelta(days=days)
    else:
        # 如果是其他格式，返回当前日期
        date = now
    
    return date.strftime('%Y-%m-%d')

category_mapp = {
    "综合": "",
    "火电": "151",
    "风电": "1",
    "水电": "103",
    "售电": "1226",
    "储能": "100150",
    "氢能": "100156",
    "智能电网": "100236",
    "核电": "65",
    "输配电": "100226",
    "能源": "101377",
    "碳管家": "100170",
    "光伏": "184"
}

#
def get_list_page(category='',keyword='风电最新态势',page=1,date_filter='2025-04-01'):
	# 默认按时间最新排序 1页20个
	url = 'https://news.bjx.com.cn/search/?industry={}&kw={}&type=5&sort=2&index={}&ajax=1'.format(category_mapp.get(category),keyword,page)
	# print(url)

	headers = {
		"Host": "news.bjx.com.cn",
		"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0",
		"Accept": "*/*",
		"Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
		"Accept-Encoding": "gzip, deflate, br, zstd",
		"X-Requested-With": "XMLHttpRequest",
	}
	resp = requests.get(url,headers=headers)
	soup = BeautifulSoup(resp.text,'lxml')
	# 获取总共条数
	result_count_num = soup.find('div',class_='cc-list')['data-num']
	# 下一页标签
	next_page_flag = soup.find('div',class_='cc-paging').find_all('a')[-1]['class']
	# print(result_count_num,next_page_flag)
	item_list = []
	for div in soup.find_all('div',class_='item'):
		item = {
			'title': div.find('div', class_='top').a.text.strip(),
			'url': div.find('div', class_='top').a['href'].strip(),
			'source': div.find('div', class_='bottom').find_all('em')[0].text.strip().replace('来源：',''),
			'date': convert_relative_time(div.find('div', class_='bottom').find_all('em')[-1].text.strip()),
			'introduction': div.find('span', class_='max-2').text.strip(),
			'image':div.find('div', class_='left').a.img['src'] if div.find('div', class_='left') else '',
			'labels':  [a.text.strip() for a in div.find('div', class_='right').div.find_all('a')]
		}

		# 判断是否早于筛选时间
		if datetime.strptime(date_filter, "%Y-%m-%d") > datetime.strptime(item['date'], "%Y-%m-%d"):
			break

		# 根据文章链接爬取详情页
		result = get_detail_page(item['url'])
		item.update(result)
		# print(item)
		item_list.append(item)
	return item_list,next_page_flag

def get_detail_page(url):
	# print(url)
	headers = {
		# 因为可能有多个不同的网站的详情页
		# "Host": "news.bjx.com.cn",
		"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0",
		"Accept": "*/*",
		"Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
		"Accept-Encoding": "gzip, deflate, br, zstd",
		"X-Requested-With": "XMLHttpRequest",
	}
	resp = requests.get(url,headers=headers)
	# 文章不存在或者黑名单文章
	if resp.status_code == 404 or url in ['https://guangfu.bjx.com.cn/b2b/gfhq/517878.html']:
		return {}
	# with open('cs.html','w',encoding='utf-8') as f:
	# 	f.write(resp.text)
	soup = BeautifulSoup(resp.text,'lxml')
	item = {
		'detail_time': soup.find('div', class_='cc-headline').div.p.find_all('span')[0].text.strip(),
		'author': soup.find('div', class_='cc-headline').div.p.find_all('span')[2].text.strip().replace('作者：',''),
		'content':soup.find('div', class_='cc-article').text.strip(),
		'image_list':[img.get('src') for img in soup.find('div', class_='cc-article').find_all('img') if img.get('src')]
	}
	# print(item)
	return item

def get_bjx_news(category, keyword, date_filter, limit):
	# print('北极星',keyword, date_filter, limit)
	article_list = []

	# 测试详情页爬取
	# get_detail_page('https://news.bjx.com.cn/html/20250219/1427954.shtml')

	for page in range(1, 10000):
		item_list, next_page_flag = get_list_page(category, keyword, page,date_filter)
		article_list = article_list + item_list
		# 判断是否有下一页
		if next_page_flag:
			# print('没有下一页')
			break
		# 判断是否还有满足日期筛选的文章未爬取
		if len(item_list) < 20:
			# print('没有满足日期筛选的文章未爬取')
			break
		# 判断是否爬取超过指定条数
		if len(article_list) >= limit:
			# print('爬取超过指定条数')
			break

	# for item in article_list:
	# 	print(item['date'],item['title'])
	article_list = article_list[:limit]
	return article_list


if __name__ == '__main__':
	category = '综合'
	keyword = '风电'
	date_filter = '2025-04-17'
	article_list = get_bjx_news(category, keyword, date_filter, 1)
	print(len(article_list),article_list)
