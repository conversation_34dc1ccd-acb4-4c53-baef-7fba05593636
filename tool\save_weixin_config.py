# -*- coding: utf-8 -*-
"""
微信公众号爬虫配置保存工具
用于将爬虫配置信息写入Redis
"""

import json
import redis
from config import Config

def save_weixin_config(cookie, token, fingerprint):
    """
    将微信爬虫配置保存到Redis
    
    Args:
        cookie: 登录cookie
        token: 访问令牌
        fingerprint: 指纹信息
    
    Returns:
        bool: 是否保存成功
    """
    try:
        client = redis.Redis(
            host=Config.REDIS_HOST,
            port=Config.REDIS_PORT,
            db=Config.REDIS_DB,
            password=Config.REDIS_PASSWORD,
            decode_responses=True
        )
        # 测试连接
        client.ping()
        
        config = {
            'cookie': cookie,
            'token': token,
            'fingerprint': fingerprint
        }
        # 将配置保存为JSON字符串
        client.set(Config.WEIXIN_CONFIG_KEY, json.dumps(config))
        print("微信爬虫配置已成功保存到Redis")
        return True
    except redis.ConnectionError as e:
        print(f"Redis连接失败: {str(e)}")
        return False
    except Exception as e:
        print(f"保存微信爬虫配置失败: {str(e)}")
        return False

if __name__ == '__main__':
    cookie = '_clsk=k1ewax|1748238086023|1|1|mp.weixin.qq.com/weheat-agent/payload/record; xid=558befe6bd8bf5526d8027454575e903; data_bizuin=3930995646; slave_user=gh_8ba8ed3bcb32; slave_sid=T09CNExyNjlPbDU0Sl9CczFsRmxLVkFJWXZkSmZVOVhGNlpOOTU1djJkaDVmTVdKVlFjNmhycWZEbGxqa0hTSWZTY0NtVHJZZ3dVZ2Q3bUU5ZTJNcWNqOE9pb2dyS0QxT2dmZjV6UUs1WFE2TkVzdVY0RzFBT3hRMUxvUVRQbzltVW1lUzU2dU1TUEJ1d0JH; rand_info=CAESIEGyb3whHs+wTku8oScJtvWLFuf6VTbhAx5U6XMBIqgO; data_ticket=XUMsd/lAi5Kd16j/HUo2dhiqiVFb5sO5TnUKapZoCvlE5H/aLHUEGrSz4XmSugw3; bizuin=3930995646; slave_bizuin=3930995646; mm_lang=zh_CN; _clck=3930995646|1|fw8|0; wxuin=47017515625228; ua_id=cCrsn7RrWNtgJqhIAAAAAEmjR5ltu4XFS5VCdy_uyUE='
    token = '1045011991'
    fingerprint = '40fec470fb00c214b508676e723f9725'

    save_weixin_config(cookie, token, fingerprint)