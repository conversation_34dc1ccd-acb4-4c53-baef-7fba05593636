-- 用户表
CREATE TABLE `users` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '用户主键ID',
    `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    `email` VARCHAR(100) NOT NULL UNIQUE COMMENT '用户邮箱（唯一）',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号（可选）',
    `password` VARCHAR(255) NOT NULL COMMENT '密码（加密存储）',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '用户昵称',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '用户头像URL',
    `status` TINYINT(1) DEFAULT 1 COMMENT '账号状态：1-启用，0-禁用',
    `last_login` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX (`username`),
    INDEX (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';

-- 智能体表
CREATE TABLE `agents` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '智能体主键ID',
    `name` VARCHAR(255) NOT NULL COMMENT '智能体的名称',
    `description` TEXT NOT NULL COMMENT '智能体的详细介绍',
    `category` VARCHAR(255) NOT NULL COMMENT '智能体的类别',
    `generated_content` TEXT DEFAULT NULL COMMENT '智能体生成内容',
    `userid` INT(11) NOT NULL COMMENT '创建者用户ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='智能体表';

-- 节点表
CREATE TABLE `nodes` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '节点主键ID',
    `agent_id` INT(11) NOT NULL COMMENT '所属智能体的唯一标识符',
    `node_name` VARCHAR(255) NOT NULL COMMENT '节点的名称',
    `content_style` VARCHAR(100) NOT NULL COMMENT '内容样式',
    `text_content` TEXT DEFAULT NULL COMMENT '文本组件内容',
    `summary_content` TEXT DEFAULT NULL COMMENT '节点生成的总结内容',
    `text_type` INT(11) DEFAULT 0 COMMENT 'TODO: 临时字段 - 文本类型：0-普通文本，1-特殊文本',
    `profession` VARCHAR(255) DEFAULT NULL COMMENT 'TODO: 临时字段 - 职业字段',
    `search_query` VARCHAR(1024) DEFAULT NULL COMMENT '搜索关键词',
    `source_type` VARCHAR(100) DEFAULT NULL COMMENT '来源类别：wechat(微信), website(网站), domestic(国内搜索), foreign(国外搜索)',
    `source_list` VARCHAR(512) DEFAULT NULL COMMENT '具体来源：微信公众号名称或网站链接',
    `filter_time` VARCHAR(100) DEFAULT NULL COMMENT '筛选时间',
    `filter_quantity` INT(11) DEFAULT NULL COMMENT '筛选数量',
    `is_referenced` INT(11) DEFAULT 0 COMMENT '定制输出模式：0-未引用，1-引用',
    `userid` INT(11) NOT NULL COMMENT '创建者用户ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX (`agent_id`),
    FOREIGN KEY (`agent_id`) REFERENCES `agents`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节点表';

-- 节点搜索结果表
CREATE TABLE `node_search_results` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '搜索结果主键ID',
    `title` VARCHAR(255) NOT NULL COMMENT '内容标题',
    `source` VARCHAR(255) NOT NULL COMMENT '内容来源',
    `url` VARCHAR(1024) DEFAULT NULL COMMENT '文章链接',
    `publish_time` VARCHAR(100) COMMENT '内容发布时间（字符串格式，兼容多种日期格式）',
    `content` MEDIUMTEXT NOT NULL COMMENT '内容正文（不含图片）',
    `is_selected` INT(11) DEFAULT 0 COMMENT '搜索结果是否被选中: 0-未选中, 1-已选中',
    `userid` INT(11) NOT NULL COMMENT '创建者用户ID',
    `temp_node_id` VARCHAR(36) DEFAULT NULL COMMENT '临时节点ID（UUID）',
    `node_id` INT(11) DEFAULT NULL COMMENT '正式节点ID',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_userid` (`userid`),
    INDEX `idx_temp_node_id` (`temp_node_id`),
    INDEX `idx_node_id` (`node_id`),
    INDEX `idx_is_selected` (`is_selected`),
    INDEX `idx_source` (`source`),
    INDEX `idx_userid_temp_node_id` (`userid`, `temp_node_id`),
    INDEX `idx_userid_node_id` (`userid`, `node_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节点搜索结果表';

-- 节点资源表
CREATE TABLE `node_resources` (
    `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '资源主键ID',
    `resource_path` VARCHAR(255) NOT NULL COMMENT '资源文件路径',
    `name` VARCHAR(255) NOT NULL COMMENT '文件名称',
    `file_type` VARCHAR(100) NOT NULL COMMENT '文件类型',
    `content` MEDIUMTEXT DEFAULT NULL COMMENT '文件内容文本',
    `word_count` INT(11) COMMENT '文件中的文字数量',
    `upload_time` DATETIME COMMENT '上传时间',
    `userid` INT(11) NOT NULL COMMENT '创建者用户ID',
    `temp_node_id` VARCHAR(36) DEFAULT NULL COMMENT '临时节点ID（UUID）',
    `node_id` INT(11) DEFAULT NULL COMMENT '正式节点ID',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节点资源表';


INSERT INTO `aios_api`.`users`(`id`, `username`, `email`, `phone`, `password`, `nickname`, `avatar`, `status`, `last_login`, `last_login_ip`, `created_at`, `updated_at`) VALUES (1, 'admin', '<EMAIL>', NULL, 'scrypt:32768:8:1$Nyh6g30gQAY3pqOG$d91b6a080e3c4ed6bfb2e1553e089d374e6eacf63e6569df7a36c44d56678873920878f78956d19305acb52087217976d1303646c8a62b6b82c9d976f895b44f', NULL, NULL, 1, '2025-04-23 07:40:49', '127.0.0.1', '2025-04-18 18:46:51', '2025-04-23 07:40:49');
