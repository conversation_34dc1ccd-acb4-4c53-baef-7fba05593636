import requests
import os
from models import Agent, Node, NodeSearchResult, NodeResource
from typing import List, Dict
from utils.logger import setup_logger
from utils.error_handler import APIError
from datetime import datetime
from sqlalchemy.orm import Session

logger = setup_logger('agents.services')

def create_or_update_agent(data: Dict, user_id: int, db: Session) -> Dict:
    """
    创建或更新Agent
    
    Args:
        data: 包含Agent信息的字典
            - name: Agent名称
            - description: Agent描述
            - category: Agent分类
            - agent_id: Agent ID（更新时）
        user_id: 用户ID
        
    Returns:
        Dict: 包含操作结果的字典
        
    Raises:
        APIError: 当Agent不存在或用户没有权限时
    """
    try:
        # 路由装饰器已经验证了必要字段，这里无需再次验证
        if 'agent_id' in data:
            agent = db.query(Agent).filter(Agent.id == data['agent_id'], Agent.userid == user_id).first()
            if not agent:
                logger.warning(f"Agent不存在或用户无权限访问: agent_id={data['agent_id']}, user_id={user_id}")
                raise APIError("Agent不存在或您没有权限访问此Agent", 404)
            logger.info(f"更新Agent: agent_id={data['agent_id']}, user_id={user_id}")
        else:
            agent = Agent()
            logger.info(f"创建新Agent: user_id={user_id}")
        
        # 更新Agent属性
        agent.name = data['name']
        agent.description = data['description']
        agent.category = data['category']
        agent.userid = user_id
        agent.update_time = datetime.now()
        
        if 'agent_id' not in data:
            agent.create_time = datetime.now()
        
        db.add(agent)
        db.commit()
        db.refresh(agent)
        
        return {
            'agent_id': agent.id,
            'name': agent.name,
            'description': agent.description,
            'category': agent.category,
            'create_time': agent.create_time.isoformat() if agent.create_time else None,
            'update_time': agent.update_time.isoformat() if agent.update_time else None
        }
    except APIError:
        # db.session.rollback() # Handled by FastAPI dependency
        raise
    except Exception as e:
        # db.session.rollback() # Handled by FastAPI dependency
        logger.error(f"创建/更新Agent发生错误: {str(e)}", exc_info=True)
        raise APIError(f"创建/更新Agent失败: {str(e)}", 500)

def get_agents(user_id: int, db: Session) -> List[Dict]:
    """
    获取用户的所有Agent，按创建时间倒序排序
    
    Args:
        user_id: 用户ID
        
    Returns:
        List[Dict]: Agent列表
        
    Raises:
        APIError: 当获取Agent列表失败时
    """
    try:
        agents = db.query(Agent).filter(Agent.userid == user_id).order_by(Agent.create_time.desc()).all()
        
        result = [{
            'agent_id': agent.id,
            'name': agent.name,
            'description': agent.description,
            'category': agent.category,
            'create_time': agent.create_time.isoformat() if agent.create_time else None,
            'update_time': agent.update_time.isoformat() if agent.update_time else None
        } for agent in agents]
        
        return result
    except Exception as e:
        logger.error(f"获取Agent列表发生错误: {str(e)}", exc_info=True)
        raise APIError(f"获取Agent列表失败: {str(e)}", 500)

def delete_agent(agent_id: int, user_id: int, db: Session) -> Dict:
    """
    删除指定的Agent
    
    Args:
        agent_id: Agent ID
        user_id: 用户ID
        
    Returns:
        Dict: 包含操作结果的字典
        
    Raises:
        APIError: 当Agent不存在、用户无权限或删除失败时
    """
    # 路由装饰器已经验证了agent_id参数，这里无需再次验证
    try:
        agent = db.query(Agent).filter(Agent.id == agent_id, Agent.userid == user_id).first()
        if not agent:
            logger.warning(f"Agent不存在或用户无权限删除: agent_id={agent_id}, user_id={user_id}")
            raise APIError("Agent不存在或您没有权限删除此Agent", 404)
            
        db.delete(agent)
        db.commit()
        
        return {
            'agent_id': agent_id
        }
    except APIError:
        # db.session.rollback() # Handled by FastAPI dependency
        raise
    except Exception as e:
        # db.session.rollback() # Handled by FastAPI dependency
        logger.error(f"删除Agent发生错误: {str(e)}", exc_info=True)
        raise APIError(f"删除Agent失败: {str(e)}", 500)

def run_agent(agent_id: int, user_id: int, db: Session) -> Dict:
    """
    运行智能体，直接返回构建的节点数据
    
    Args:
        agent_id: 智能体ID
        user_id: 用户ID
        
    Returns:
        Dict: 包含节点列表的字典
    
    Raises:
        APIError: 当智能体不存在、无法获取节点时
    """
    try:
        logger.info(f"开始运行智能体: agent_id={agent_id}, user_id={user_id}")
        
        # 1. 检查智能体是否存在
        agent = db.query(Agent).filter(Agent.id == agent_id, Agent.userid == user_id).first()
        if not agent:
            logger.warning(f"智能体不存在或无权访问: agent_id={agent_id}")
            raise APIError("Agent not found or access denied", 404)
        
        # 2. 获取智能体下的所有节点，按创建时间升序排序
        nodes = db.query(Node).filter(Node.agent_id == agent_id, Node.userid == user_id).order_by(Node.create_time.asc()).all()
        if not nodes:
            logger.warning(f"智能体下没有节点: agent_id={agent_id}")
            raise APIError("No nodes found for this agent", 404)
        
        # 3. 构建返回结果
        node_list = []
        
        # 处理每个节点
        for node in nodes:
            content_style = node.content_style if node.content_style else "text"
            
            # 获取引用内容
            references = []
            if getattr(node, 'is_referenced', 0) == 1:
                # 获取搜索结果引用
                search_results = db.query(NodeSearchResult).filter(
                    NodeSearchResult.node_id == node.id, 
                    NodeSearchResult.is_selected == 1
                ).all()
                
                for result in search_results:
                    if result.title and result.url:
                        references.append({
                            "title": result.title,
                            "url": result.url
                        })
                
                # # 获取资源引用
                # resources = db.query(NodeResource).filter(NodeResource.node_id == node.id).all()
                # for resource in resources:
                #     if resource.name:
                #         references.append({
                #             "title": resource.name,
                #             "url": resource.name  # 使用资源名称作为URL
                #         })
            
            node_data = {
                "node_id": node.id,
                "agent_id": node.agent_id,
                "node_name": node.node_name,
                "content_style": content_style,
                "text_content": node.text_content,
                "summary_content": node.summary_content,
                "text_type": node.text_type,
                "profession": node.profession,
                "search_query": node.search_query,
                "source_type": node.source_type,
                "source_list": node.source_list,
                "filter_time": node.filter_time,
                "filter_quantity": node.filter_quantity,
                "is_referenced": node.is_referenced,
                "create_time": node.create_time.isoformat() if node.create_time else None,
                "update_time": node.update_time.isoformat() if node.update_time else None,
                "references": references
            }
            node_list.append(node_data)
        
        return {"node_list": node_list}
    except APIError:
        raise
    except Exception as e:
        logger.error(f"运行智能体发生错误: {str(e)}", exc_info=True)
        raise APIError(f"运行智能体失败: {str(e)}", 500) 