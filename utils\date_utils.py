from datetime import datetime, timedelta
from utils.logger import setup_logger

logger = setup_logger('utils.date_utils')

def get_relative_date(filter_term: str) -> str:
    """
    根据过滤条件返回相对日期
    
    Args:
        filter_term (str): 过滤条件，如"今天"、"近一周"、"近一月"等
        
    Returns:
        str: 格式化后的日期字符串，格式为"YYYY-MM-DD"
    """
    today = datetime.today()
    
    # 支持新格式的过滤条件
    if filter_term == "今天":
        target_date = today
    elif filter_term == "近一周" or filter_term == "一周内":
        target_date = today - timedelta(days=7)
    elif filter_term == "近一月" or filter_term == "一月内":
        target_date = today - timedelta(days=30)
    elif filter_term == "不限":
        target_date = today - timedelta(days=365*10)
    # 保留原有的条件
    elif filter_term == "三天内":
        target_date = today - timedelta(days=3)
    elif filter_term == "半年内":
        target_date = today - timedelta(days=180)
    elif filter_term == "一年内":
        target_date = today - timedelta(days=365)
    else:
        # 默认为今天，而不是返回None
        logger.warning(f"未知的时间过滤条件: {filter_term}，默认使用今天")
        target_date = today
        
    return target_date.strftime("%Y-%m-%d")

def format_datetime(dt: datetime) -> str:
    """
    格式化日期时间为字符串
    
    Args:
        dt (datetime): 日期时间对象
        
    Returns:
        str: 格式化后的日期时间字符串
        
    Raises:
        ValueError: 当输入参数不是datetime类型时抛出
    """
    try:
        logger.debug(f"Formatting datetime: {dt}")
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except AttributeError as e:
        logger.error(f"Invalid datetime object: {str(e)}")
        raise ValueError("Input must be a datetime object")

def parse_datetime(dt_str: str) -> datetime:
    """
    解析日期时间字符串为datetime对象
    
    Args:
        dt_str (str): 日期时间字符串
        
    Returns:
        datetime: 解析后的日期时间对象
        
    Raises:
        ValueError: 当字符串格式不正确时抛出
    """
    try:
        logger.debug(f"Parsing datetime string: {dt_str}")
        return datetime.strptime(dt_str, '%Y-%m-%d %H:%M:%S')
    except ValueError as e:
        logger.error(f"Invalid datetime string format: {str(e)}")
        raise ValueError("Invalid datetime string format") 