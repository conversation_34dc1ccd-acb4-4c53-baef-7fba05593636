import os
import time
from datetime import datetime
from typing import List, Dict
from utils.logger import setup_logger
from utils.error_handler import APIError
from utils.document_loader import read_file_content
from utils.url_processor import process_url
from models import NodeResource, Node
import requests
from sqlalchemy.orm import Session
from fastapi import UploadFile # 导入UploadFile，用于文件上传

logger = setup_logger('resources.services')

# 最大文件大小限制，20MB
MAX_FILE_SIZE = 20 * 1024 * 1024

def process_urls(data: Dict, user_id: int, db: Session) -> List[Dict]:
    """
    处理URL列表
    
    Args:
        data: 包含请求数据的字典
            - urls: URL列表（必选）
            - temp_node_id: 临时节点ID（和node_id至少提供一个）
            - node_id: 正式节点ID（和temp_node_id至少提供一个）
        user_id: 用户ID
        
    Returns:
        List[Dict]: 处理结果列表，每个字典包含以下字段：
            - resource_id: 资源ID
            - file_name: URL
            - file_type: 文件类型（固定为"网页"）
            - word_count: 字数
            - upload_time: 上传时间
        
    Raises:
        APIError: 当处理过程中发生错误时抛出
    """
    try:  
        # 获取并验证temp_node_id和node_id
        temp_node_id = data.get('temp_node_id')
        node_id = data.get('node_id')
        if not temp_node_id and not node_id:
            error_msg = "必须提供临时节点ID(temp_node_id)或正式节点ID(node_id)中的至少一个"
            logger.warning(error_msg)
            raise APIError(error_msg, 400)
            
        # 如果node_id存在，验证node表中是否存在该记录
        if node_id:
            node = db.query(Node).filter(Node.id==node_id, Node.userid==user_id).first()
            if not node:
                error_msg = f"节点不存在或无权限访问: node_id={node_id}"
                logger.warning(error_msg)
                raise APIError(error_msg, 404)
            # 如果node_id存在，将temp_node_id设为空字符串
            temp_node_id = ''
            
        # 处理URLs
        urls = data['urls'] # data['urls'] 已经是列表，不需要split
        
        # 处理URL，移除末尾的斜杠
        processed_urls = []
        for url in urls:
            # 清理URL：移除空白字符和末尾斜杠
            cleaned_url = url.strip()
            if cleaned_url:  # 确保URL非空
                # 移除末尾斜杠
                if cleaned_url.endswith('/'):
                    cleaned_url = cleaned_url[:-1]
                processed_urls.append(cleaned_url)
        
        # 记录节点信息
        node_info = f"临时节点ID: {temp_node_id}" if temp_node_id else f"正式节点ID: {node_id}"
        logger.info(f"处理URL列表，{node_info}，URL数量: {len(processed_urls)}")
        
        # 保存所有URLs到数据库
        resources_info = []
        failed_urls = []
        success_count = 0
        
        if not processed_urls:
            # 没有有效的URL
            raise APIError("未提供有效的URL", 400)
            
        # 遍历所有URL并保存到数据库
        for url in processed_urls:
            current_time = datetime.now()
            
            try:
                # 处理URL，获取内容和字数
                url_result = process_url(url)
                word_count = url_result["word_count"]
                content = url_result["content"]
                
                # 创建资源记录
                if node_id:
                    resource = NodeResource(
                        resource_path='',      # URL资源没有本地路径
                        name=url,              # URL作为名称
                        file_type='网页',       # 固定为"网页"类型
                        content=content,       # 网页内容
                        word_count=word_count, # 实际字数
                        upload_time=current_time,     # 上传时间
                        userid=user_id,               # 用户ID
                        temp_node_id=temp_node_id,    # 临时节点ID
                        node_id=node_id               # 正式节点ID
                    )
                else:
                    resource = NodeResource(
                        resource_path='',  # URL资源没有本地路径
                        name=url,  # URL作为名称
                        file_type='网页',  # 固定为"网页"类型
                        content=content,  # 网页内容
                        word_count=word_count,  # 实际字数
                        upload_time=current_time,  # 上传时间
                        userid=user_id,  # 用户ID
                        temp_node_id=temp_node_id  # 临时节点ID
                    )
                
                # 保存到数据库
                db.add(resource)
                db.flush()  # 刷新会话以获取ID，但不提交
                
                # 添加到结果列表
                resources_info.append({
                    'resource_id': resource.id,  # 确保包含资源ID
                    'file_name': url,
                    'file_type': '网页',
                    'word_count': word_count,
                    'upload_time': current_time.isoformat()
                })
                success_count += 1
                
            except ValueError as e:
                # URL格式无效或内容为空时，记录失败但继续处理其他URL
                logger.warning(f"处理URL失败: {url}, 错误: {str(e)}")
                failed_urls.append({"url": url, "error": str(e)})
                
                # 如果是内容为空的错误，仍然创建资源但内容设为空
                if "内容为空" in str(e):
                    # 创建空内容的资源记录
                    resource = NodeResource(
                        resource_path='',      # URL资源没有本地路径
                        name=url,              # URL作为名称
                        file_type='网页',       # 固定为"网页"类型
                        content='',            # 空内容
                        word_count=0,          # 字数为0
                        upload_time=current_time,     # 上传时间
                        userid=user_id,               # 用户ID
                        temp_node_id=temp_node_id,    # 临时节点ID
                        node_id=node_id               # 正式节点ID
                    )
                    
                    # 保存到数据库
                    db.add(resource)
                    db.flush()  # 刷新会话以获取ID，但不提交
                    
                    # 添加到结果列表
                    resources_info.append({
                        'resource_id': resource.id,  # 确保包含资源ID
                        'file_name': url,
                        'file_type': '网页',
                        'word_count': 0,
                        'upload_time': current_time.isoformat(),
                        'status': 'empty_content'
                    })
                    success_count += 1
                
            except requests.exceptions.RequestException as e:
                # 网络请求错误，记录失败
                logger.warning(f"请求URL失败: {url}, 错误: {str(e)}")
                failed_urls.append({"url": url, "error": f"网络请求错误: {str(e)}"})
                
            except Exception as e:
                # 其他错误，记录失败
                logger.error(f"处理URL时发生未知错误: {url}, 错误: {str(e)}", exc_info=True)
                failed_urls.append({"url": url, "error": f"未知错误: {str(e)}"})
        
        # 检查是否至少有一个URL处理成功
        if success_count == 0 and len(failed_urls) > 0:
            # 所有URL都处理失败，回滚并抛出异常
            # db.rollback() # Handled by FastAPI dependency
            # 提取第一个失败URL的错误信息作为主要错误信息
            main_error = failed_urls[0]["error"]
            raise APIError(f"所有URL处理均失败。示例错误: {main_error}", 400)
        
        # 最后提交所有更改
        db.commit()
        
        # 如果有失败的URL，在日志中记录
        if failed_urls:
            logger.info(f"部分URL处理失败，成功: {success_count}，失败: {len(failed_urls)}")
        
        # 返回所有成功处理的资源信息
        return resources_info
            
    except APIError:
        # 直接向上传递API错误
        raise
    except Exception as e:
        logger.error(f"处理URL列表失败: {str(e)}", exc_info=True)
        # db.rollback() # Handled by FastAPI dependency
        raise APIError(f"处理URL列表失败: {str(e)}", 500)

async def upload_file_and_process(file_stream: UploadFile, file_data: Dict, user_id: int, db: Session) -> Dict:
    """
    处理文件上传请求
    """
    try:
        file_name = file_data['file_name']
        temp_node_id = file_data.get('temp_node_id')
        node_id = file_data.get('node_id')
        
        # 验证temp_node_id或node_id必须提供一个
        if not temp_node_id and not node_id:
            error_msg = "文件上传必须提供临时节点ID(temp_node_id)或正式节点ID(node_id)中的至少一个"
            logger.warning(error_msg)
            raise APIError(error_msg, 400)
        
        # 如果node_id存在，验证node表中是否存在该记录
        if node_id:
            node = db.query(Node).filter(Node.id==node_id, Node.userid==user_id).first()
            if not node:
                error_msg = f"节点不存在或无权限访问: node_id={node_id}"
                logger.warning(error_msg)
                raise APIError(error_msg, 404)
            # 如果node_id存在，将temp_node_id设为空字符串
            temp_node_id = ''
        
        # 确保保存文件的目录存在
        upload_dir = os.path.join(os.getcwd(), "uploads") # 假设上传文件保存到项目根目录的uploads文件夹
        os.makedirs(upload_dir, exist_ok=True)
        
        file_path = os.path.join(upload_dir, file_name)
        current_time = datetime.now()
        
        try:
            # 将文件流保存到本地文件
            file_content_bytes = await file_stream.read()
            with open(file_path, "wb") as f:
                f.write(file_content_bytes)
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size > MAX_FILE_SIZE:
                os.remove(file_path) # 删除过大文件
                raise APIError(f"文件大小超出限制 ({MAX_FILE_SIZE / (1024 * 1024)}MB)", 400)
            
            # 读取文件内容并获取字数和文件类型
            content_info = read_file_content(file_path)
            file_type = content_info['file_type']
            word_count = content_info['word_count']
            file_content_str = content_info['content']
            
            # 创建资源记录
            if node_id:
                resource = NodeResource(
                    resource_path=file_path, 
                    name=file_name,
                    file_type=file_type,
                    content=file_content_str,
                    word_count=word_count,
                    upload_time=current_time,
                    userid=user_id,
                    temp_node_id=temp_node_id,
                    node_id=node_id
                )
            else:
                resource = NodeResource(
                    resource_path=file_path, 
                    name=file_name,
                    file_type=file_type,
                    content=file_content_str,
                    word_count=word_count,
                    upload_time=current_time,
                    userid=user_id,
                    temp_node_id=temp_node_id
                )
            
            db.add(resource)
            db.commit()
            db.refresh(resource) # 刷新以获取可能由数据库生成的字段，例如ID
            
            logger.info(f"文件上传成功: {file_name}, type: {file_type}, word_count: {word_count}")
            return {
                'resource_id': resource.id,
                'file_name': file_name,
                'file_type': file_type,
                'word_count': word_count,
                'upload_time': current_time.isoformat()
            }
            
        except ValueError as e:
            # 如果文件类型不支持或内容为空，删除文件并返回错误
            try:
                os.remove(file_path)
            except:
                logger.warning(f"无法删除临时文件: {file_path}")
            raise APIError(f"文件处理失败: {str(e)}", 400)
        except Exception as e:
            # 其他错误，如IO错误等
            try:
                os.remove(file_path)
            except:
                logger.warning(f"无法删除临时文件: {file_path}")
            raise APIError(f"文件处理失败: {str(e)}", 500)
        
    except APIError:
        # db.rollback() # Handled by FastAPI dependency
        if os.path.exists(file_path):
            os.remove(file_path) # 处理失败时删除已上传的文件
        raise
    except Exception as e:
        logger.error(f"文件处理失败: {str(e)}", exc_info=True)
        # db.rollback() # Handled by FastAPI dependency
        if os.path.exists(file_path):
            os.remove(file_path) # 出现未知错误时删除已上传的文件
        raise APIError(f"文件处理失败: {str(e)}", 500)

def delete_resource(resource_id: int, user_id: int, db: Session) -> Dict:
    """
    删除指定资源
    """
    try:
        resource = db.query(NodeResource).filter(NodeResource.id == resource_id, NodeResource.userid == user_id).first()
        if not resource:
            logger.warning(f"资源不存在或无权限: resource_id={resource_id}, user_id={user_id}")
            raise APIError("Resource not found or permission denied", 404)
        
        # 如果是文件类型资源，尝试删除物理文件
        if resource.resource_path:
            try:
                file_path = os.path.join(os.getcwd(), resource.resource_path)
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.info(f"删除资源文件: {file_path}")
            except Exception as e:
                logger.warning(f"删除资源文件失败: {file_path}, 错误: {str(e)}", exc_info=True)
        
        db.delete(resource)
        db.commit()
        
        logger.info(f"资源删除成功: resource_id={resource.id}")
        return {'resource_id': resource.id}
        
    except APIError:
        raise
    except Exception as e:
        logger.error(f"删除资源失败: {str(e)}", exc_info=True)
        db.rollback() # Only rollback on unexpected errors, not APIError
        raise APIError(f"删除资源失败: {str(e)}", 500) 