import requests
from utils.url_processor import extract_text_from_url
import time
import random

def translate_to_english(text):
    """
    将中文文本翻译成英文
    
    Args:
        text: 需要翻译的文本（通常是中文）
        
    Returns:
        翻译后的英文文本
    """
    try:
        # 替换googletrans库，使用更可靠的直接调用谷歌翻译API方式
        url = "https://translate.googleapis.com/translate_a/single"
        params = {
            "client": "gtx",
            "sl": "auto",  # 源语言自动检测
            "tl": "en",    # 目标语言：英文
            "dt": "t",     # 要翻译文本
            "q": text
        }
        
        # 使用与搜索相同的代理
        proxies = {
            "http": "http://127.0.0.1:7890",
            "https": "http://127.0.0.1:7890"
        }
        
        # 使用requests库发送请求
        response = requests.get(url, params=params, proxies=proxies)
        if response.status_code == 200:
            # 解析响应
            result = response.json()
            # 提取翻译结果
            translated_text = ""
            for item in result[0]:
                if item[0]:
                    translated_text += item[0]
            print(f"原始文本: {text}")
            print(f"翻译结果: {translated_text}")
            return translated_text
        else:
            print(f"翻译请求失败，状态码: {response.status_code}")
            return text
    except Exception as e:
        print(f"翻译出错: {str(e)}")
        return text  # 如果翻译失败，返回原文本


def get_google_api(query, date_filter, limit):
    '''
    {
      'kind': ...,           # 类型标识（customsearch#result）
      'title': ...,          # 搜索结果标题
      'htmlTitle': ...,      # 带 HTML 高亮标签的标题（加了 <b> 标签）
      'link': ...,           # 实际网页链接（URL）
      'displayLink': ...,    # 显示的域名（简短）
      'snippet': ...,        # 页面摘要（纯文本）
      'htmlSnippet': ...,    # 页面摘要（带 HTML 高亮）
      'formattedUrl': ...,   # 显示用的 URL
      'htmlFormattedUrl': ...,  # 显示用 URL（带高亮）
      'pagemap': {...}       # 页面相关的元数据（如 metatags）
    }
    '''

    english_query = translate_to_english(query)

    api_key = "AIzaSyDxv7pxUecbfqMeBfqbD1avBPj3Cj-Dlc0"
    cse_id = "53b1e03ac04a34502"
    url = f"https://www.googleapis.com/customsearch/v1?key={api_key}&cx={cse_id}&q={english_query}"
    proxies = {
        "http": "http://127.0.0.1:7890",
        "https": "http://127.0.0.1:7890"
    }
    response = requests.get(url, proxies=proxies)
    results = response.json()
    web_list = []
    
    for item in results.get("items", []):
        # 准备基本信息
        article_info = {
            'title': item["title"],
            'content': item["snippet"],
            'url': item["link"],
            'site': item["displayLink"],
            'detail_content': ''  # 默认为空字符串
        }
        
        # 尝试爬取详细内容
        try:
            # 添加随机延时，避免请求过于频繁
            time.sleep(random.uniform(0, 1))
            
            # 使用extract_text_from_url爬取详细内容
            detail_content = extract_text_from_url(item["link"])
            
            # 如果成功获取内容，则添加到结果中
            if detail_content:
                article_info['detail_content'] = detail_content
            else:
                print(f"从URL获取的内容为空: {item['link']}")
        except Exception as e:
            print(f"爬取详细内容失败: {item['link']} - {str(e)}")
            # 即使爬取失败，仍然添加其他信息
        
        web_list.append(article_info)
        
        # 如果已经达到限制数量，则停止处理
        if len(web_list) >= limit:
            break
            
    return web_list

if __name__ == '__main__':
    query = "关税政策"
    date_filter = '2025-04-17'
    web_list = get_google_api(query, date_filter, 3)
    print(len(web_list))
    for page in web_list:
        print(f"标题: {page['title']}")
        print(f"URL: {page['url']}")
        print(f"详细内容长度: {len(page['detail_content'])}")
        print(page['content'])
        print(page['detail_content'])
        print("-" * 50)