from models import Node, NodeResource, NodeSearchResult, Agent
from typing import List, Dict
import json
from utils.logger import setup_logger
from utils.error_handler import APIError
import os
import requests
from sqlalchemy.orm import Session

logger = setup_logger('nodes.services')

def create_or_update_node(data: Dict, user_id: int, db: Session) -> Dict:
    """
    创建或更新Node
    
    Args:
        data: 包含Node信息的字典
        user_id: 用户ID
        db: SQLAlchemy Session
        
    Returns:
        包含操作结果的字典，包含node_id字段
    """
    try:
        # 确定是创建还是更新操作
        is_update = True if data.get('node_id') else False
        temp_node_id = data['temp_node_id']
        
        # 获取或创建节点对象
        if is_update:
            node = db.query(Node).filter(Node.id == data['node_id'], Node.userid == user_id).first()
            if not node:
                logger.warning(f"节点不存在或无权限: node_id={data['node_id']}, user_id={user_id}")
                raise APIError("Node not found or permission denied", 404)
        else:
            node = Node()

        # 设置基本字段
        node.agent_id = data['agent_id']
        node.node_name = data['node_name']
        node.content_style = data['content_style']
        node.userid = user_id
        node.summary_content = data['summary_content']

        # 设置文本组件
        node.text_content = data.get('text_content', '')

        # 处理搜索内容
        _handle_search_content(node, data, user_id, is_update, temp_node_id, db)
        
        # 处理上传资源启用状态
        _handle_upload_enabled(node, data, user_id, is_update, temp_node_id, db)
        
        # 保存节点
        db.add(node)
        
        # 获取新节点ID
        if not is_update:
            db.flush()
            logger.info(f"创建新节点成功，获取ID: node_id={node.id}")
        
        # 更新关联记录
        _update_related_records(node.id, temp_node_id, user_id, data.get('is_upload_enabled', True), db)
        
        # 提交所有更改
        db.commit()
        db.refresh(node)
        
        logger.info(f"节点{'更新' if is_update else '创建'}成功: node_id={node.id}")
        return {'node_id': node.id}
        
    except APIError as e:
        logger.error(f"创建/更新节点失败: {str(e)}", exc_info=True)
        # db.session.rollback() # Handled by FastAPI dependency
        raise
    except Exception as e:
        logger.error(f"创建/更新节点失败: {str(e)}", exc_info=True)
        # db.session.rollback() # Handled by FastAPI dependency
        raise APIError(f"Failed to create/update node: {str(e)}", 500)

def _handle_search_content(node, data, user_id, is_update, temp_node_id, db: Session):
    """处理节点的搜索内容部分"""
    # 更新时先重置文章选中状态
    if is_update:
        db.query(NodeSearchResult).filter(
            NodeSearchResult.node_id == data['node_id'],
            NodeSearchResult.userid == user_id
        ).update({'is_selected': 0}, synchronize_session='fetch')

    if 'search_content' in data:
        search_content = data['search_content']
        
        # 设置搜索相关字段
        node.search_query = search_content['search_query']
        node.source_type = search_content['source_type']
        node.source_list = json.dumps(search_content.get('source_list'))
        node.filter_time = search_content.get('filter_time')
        node.filter_quantity = search_content['filter_quantity']
        node.is_referenced = search_content['is_referenced']
        
        # 处理选中的文章ID
        selected_article_ids = search_content.get('selected_article_ids', [])

        # 标记选中的文章
        if selected_article_ids:
            db.query(NodeSearchResult).filter(
                NodeSearchResult.id.in_(selected_article_ids),
                NodeSearchResult.userid == user_id,
                NodeSearchResult.temp_node_id == temp_node_id
                # Or NodeSearchResult.node_id == data.get('node_id') if update
            ).update({'is_selected': 1}, synchronize_session='fetch')
    else:
        # 没有搜索配置时，清空所有搜索相关字段
        node.search_query = None
        node.source_type = None
        node.source_list = None
        node.filter_time = None
        node.filter_quantity = None
        node.is_referenced = 0

def _handle_upload_enabled(node, data, user_id, is_update, temp_node_id, db: Session):
    """处理节点的上传资源启用状态"""
    is_upload_enabled = data.get('is_upload_enabled', True)  # 默认启用
    
    if not is_upload_enabled:
        # 如果不启用上传，清除已有的关联关系
        if is_update:
            # 更新操作：清除该节点的所有资源关联
            db.query(NodeResource).filter(
                NodeResource.node_id == data['node_id'],
                NodeResource.userid == user_id
            ).update({
                'node_id': None,
                'temp_node_id': ''
            }, synchronize_session='fetch')
        else:
            # 创建操作：清除临时节点的资源关联
            if temp_node_id:
                db.query(NodeResource).filter(
                    NodeResource.temp_node_id == temp_node_id,
                    NodeResource.userid == user_id
                ).update({
                    'temp_node_id': ''
                }, synchronize_session='fetch')

def _update_related_records(node_id, temp_node_id, user_id, is_upload_enabled, db: Session):
    """更新与节点相关的临时记录"""
    if temp_node_id:
        # 只有启用上传时才更新资源记录关联
        if is_upload_enabled:
            db.query(NodeResource).filter(
                NodeResource.temp_node_id==temp_node_id,
                NodeResource.userid==user_id
            ).update({
                'node_id': node_id,
                'temp_node_id': ''
            })
    
        # 始终更新临时节点关联的搜索结果
        db.query(NodeSearchResult).filter(
            NodeSearchResult.temp_node_id == temp_node_id,
            NodeSearchResult.userid == user_id
        ).update({
            'node_id': node_id,
            'temp_node_id': ''
        })

def get_nodes(agent_id: int, user_id: int, db: Session) -> Dict:
    """
    获取指定Agent的所有Node
    
    Args:
        agent_id: Agent ID
        user_id: 用户ID
        db: SQLAlchemy Session
        
    Returns:
        Node列表，其中包含agent.generated_content字段
    
    Raises:
        APIError: 当Agent不存在时
    """
    try:
        logger.info(f"获取节点列表: agent_id={agent_id}, user_id={user_id}")
        
        # 验证Agent是否存在
        agent = db.query(Agent).filter(Agent.id == agent_id).first()
        if not agent:
            logger.warning(f"Agent不存在: agent_id={agent_id}")
            raise APIError("Agent not found", 404)
        
        nodes = db.query(Node).filter(Node.userid == user_id, Node.agent_id == agent_id).order_by(Node.create_time.asc()).all()

        result = [{
            'node_id': node.id,
            'node_name': node.node_name,
            'content_style': node.content_style,
            'text_content': node.text_content,
            'summary_content': node.summary_content
        } for node in nodes]
        
        # 处理并添加generated_content字段
        if agent.generated_content:
            try:
                generated_content_data = json.loads(agent.generated_content)
                # 确保这些键存在，避免KeyError
                file_name = generated_content_data.get('file_name')
                file_size = generated_content_data.get('file_size')
                file_content = generated_content_data.get('file_content')

                result_generated_content = {
                    "file_name": file_name,
                    "file_size": file_size,
                    "file_content": file_content
                }
            except json.JSONDecodeError:
                logger.warning(f"Agent generated_content JSON解析失败: {agent.generated_content}")
                result_generated_content = {}
        else:
            result_generated_content = {}
        
        return {"nodes": result, "generated_content": result_generated_content}
    except APIError:
        raise
    except Exception as e:
        logger.error(f"获取节点列表发生错误: {str(e)}", exc_info=True)
        raise APIError(f"获取节点列表失败: {str(e)}", 500)

def get_node(node_id: int, user_id: int, db: Session) -> Dict:
    """
    获取单个节点的详细信息
    
    Args:
        node_id: 节点ID
        user_id: 用户ID
        db: SQLAlchemy Session
        
    Returns:
        Dict: 节点详细信息
    
    Raises:
        APIError: 当节点不存在或用户无权限访问时
    """
    try:
        logger.info(f"获取节点详情: node_id={node_id}, user_id={user_id}")
        
        node = db.query(Node).filter(Node.id == node_id, Node.userid == user_id).first()
        if not node:
            logger.warning(f"节点不存在或无权访问: node_id={node_id}")
            raise APIError("Node not found or access denied", 404)

        # 获取搜索结果数据
        search_content_result = db.query(NodeSearchResult).filter(NodeSearchResult.node_id == node_id).all()
        search_content_list = [{
            'id': r.id,
            'title': r.title,
            'url': r.url,
            'source': r.source,
            'content': r.content,
            'publish_time': r.publish_time,
            'is_selected': r.is_selected
        } for r in search_content_result]

        # 获取节点资源
        resources = db.query(NodeResource).filter(NodeResource.node_id == node_id).all()
        resource_list = [{
            'resource_id': r.id,
            'resource_path': r.resource_path,
            'file_name': r.name,
            'file_type': r.file_type,
            'word_count': r.word_count,
            'upload_time': r.upload_time
        } for r in resources]

        # 构建返回数据
        result = {
            'node_id': node.id,
            'agent_id': node.agent_id,
            'node_name': node.node_name,
            'content_style': node.content_style,
            'summary_content': node.summary_content
        }

        # 构建search_content对象
        search_content_obj = {
            'search_query': node.search_query,
            'source_type': node.source_type,
            'source_list': node.source_list,
            'filter_time': node.filter_time,
            'filter_quantity': node.filter_quantity,
            'is_referenced': node.is_referenced,
            'search_content_list': search_content_list
        }

        # 添加到结果中
        if node.text_content:
            result['text_content'] = node.text_content
        if search_content_list:
            result['search_content'] = search_content_obj
        if resource_list:
            result['knowledge_base'] = resource_list
        
        logger.info(f"成功获取节点详情: node_id={node_id}")
        return result
    except APIError:
        raise
    except Exception as e:
        logger.error(f"获取节点详情发生错误: {str(e)}", exc_info=True)
        raise APIError(f"获取节点详情失败: {str(e)}", 500)

def delete_node(node_id: int, user_id: int, db: Session) -> Dict:
    """
    删除指定的Node及其关联的搜索结果和资源记录
    
    Args:
        node_id: Node ID
        user_id: 用户ID
        db: SQLAlchemy Session
        
    Returns:
        包含操作结果的字典
        
    Raises:
        APIError: 当节点不存在或用户无权限时
    """
    try:
        logger.info(f"删除节点: node_id={node_id}, user_id={user_id}")

        # 查找节点
        node = db.query(Node).filter(Node.id == node_id, Node.userid == user_id).first()
        if not node:
            logger.warning(f"节点不存在或无权限: node_id={node_id}, user_id={user_id}")
            raise APIError("Node not found or permission denied", 404)

        try:
            # 1. 删除关联的搜索结果
            search_count = db.query(NodeSearchResult).filter(NodeSearchResult.node_id == node_id).delete(synchronize_session='fetch')
            logger.info(f"删除节点关联的搜索结果: node_id={node_id}, count={search_count}")

            # 2. 删除关联的资源记录
            resources = db.query(NodeResource).filter(NodeResource.node_id == node_id).all()
            for resource in resources:
                # 如果是文件类型资源，尝试删除物理文件
                if resource.resource_path:
                    try:
                        file_path = os.path.join(os.getcwd(), resource.resource_path)
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            logger.info(f"删除资源文件: {file_path}")
                    except Exception as e:
                        logger.warning(f"删除资源文件失败: {file_path}, error: {str(e)}")
            
            # 删除资源记录
            resource_count = db.query(NodeResource).filter(NodeResource.node_id == node_id).delete(synchronize_session='fetch')
            logger.info(f"删除节点关联的资源记录: node_id={node_id}, count={resource_count}")

            # 3. 删除节点本身
            db.delete(node)
            
            # 提交所有更改
            db.commit()
            logger.info(f"节点及关联数据删除成功: node_id={node_id}, search_count={search_count}, resource_count={resource_count}")

            return {
                'node_id': node_id,
                'search_count': search_count,
                'resource_count': resource_count
            }
            
        except Exception as e:
            logger.error(f"删除节点关联数据失败: {str(e)}")
            db.rollback()
            raise APIError(f"删除节点关联数据失败: {str(e)}", 500)

    except APIError:
        raise
    except Exception as e:
        logger.error(f"删除节点失败: {str(e)}")
        db.rollback()
        raise APIError(f"Failed to delete node: {str(e)}", 500)

def run_node(data: Dict, user_id: int, db: Session) -> Dict:
    """
    运行节点生成总结内容
    
    Args:
        data: 节点数据，包含以下字段:
            - text_content: 文本内容（可选）
            - article_list: 文章列表（可选），每个元素包含：
                - title: 文章标题
                - url: 文章链接
                - content: 文章内容
            - resource_id_list: 资源ID列表（可选）
        user_id: 用户ID
        db: SQLAlchemy Session
        
    Returns:
        Dict: 包含生成的总结内容
        
    Raises:
        APIError: 当调用summary接口失败或返回异常时
    """
    try:
        logger.info(f"开始运行节点: user_id={user_id}")
        
        # 获取输入参数
        text_content = data.get('text_content', '')
        article_list = data.get('article_list', [])
        resource_id_list = data.get('resource_id_list', [])
        
        # 获取资源内容
        knowledge_base = []
        if resource_id_list:
            resources = db.query(NodeResource).filter(
                NodeResource.id.in_(resource_id_list),
                NodeResource.userid == user_id
            ).all()
            
            # 检查是否所有资源都找到了
            found_ids = {r.id for r in resources}
            missing_ids = set(resource_id_list) - found_ids
            if missing_ids:
                raise APIError(f"以下资源ID不存在或无权访问: {list(missing_ids)}", 404)
            
            knowledge_base = [resource.content for resource in resources]

        # 准备请求数据
        input_data = {
            'text_content': text_content,
            'article_list': article_list,
            'knowledge_base': knowledge_base
        }
        print(input_data)

        # 调用summary接口
        try:
            url = 'http://192.168.10.60:9003/node/summary'
            response = requests.post(url, json=input_data)  # 添加超时设置 , timeout=30
            
            # 检查HTTP状态码
            response.raise_for_status()
            
            # 解析响应数据
            result = response.json()
            
            # 检查返回的数据结构和状态码
            if result.get('code') != 200:
                error_msg = result.get('message', '调用summary接口失败')
                logger.error(f"Summary接口返回错误: {error_msg}")
                raise APIError(f"生成摘要失败: {error_msg}", 500)
            
            # 获取summary内容
            summary_content = result.get('data', {}).get('summary', '')
            if not summary_content:
                logger.warning("Summary接口返回的内容为空")
                summary_content = "未能生成有效的总结内容"

        except requests.exceptions.Timeout:
            logger.error("调用summary接口超时")
            raise APIError("生成摘要超时，请稍后重试", 504)
        except requests.exceptions.RequestException as e:
            logger.error(f"调用summary接口失败: {str(e)}")
            raise APIError(f"调用摘要服务失败: {str(e)}", 500)
        except ValueError as e:  # JSON解析错误
            logger.error(f"解析summary接口响应失败: {str(e)}")
            raise APIError("解析摘要服务响应失败", 500)
        
        logger.info("节点运行成功，已生成总结内容")
        return {
            'summary_content': summary_content
        }
    except APIError:
        raise
    except Exception as e:
        logger.error(f"运行节点失败: {str(e)}")
        raise APIError(f"Failed to run node: {str(e)}", 500)
