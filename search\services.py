from typing import List, Dict
from tool.api_wrapper import (
    get_weixin_articles_with_timeout,
    get_bjx_news_with_timeout,
    get_google_api_with_timeout,
    get_ai_model_query_results_with_timeout
)
from utils.date_utils import get_relative_date
from utils.logger import setup_logger
from models import NodeSearchResult
from datetime import datetime
import time
from sqlalchemy.orm import Session

logger = setup_logger('search.services')

def save_search_results(results: List[Dict], user_id: int, search_query: str, source_type: str, source_list: List[str], db: Session, temp_node_id: str = None, node_id: int = None) -> List[Dict]:
    """
    将搜索结果保存到NodeSearchResult表，并在results中添加ID字段
    
    Args:
        results: 搜索结果列表
        user_id: 用户ID
        search_query: 搜索关键词
        source_type: 来源类型
        source_list: 来源列表
        temp_node_id: 临时节点ID（和node_id至少提供一个）
        node_id: 正式节点ID（和temp_node_id至少提供一个）
        
    Returns:
        List[Dict]: 添加了ID字段的原始结果列表
    """
    try:
        # 先删除该临时节点ID下的旧数据
        if temp_node_id:
            db.query(NodeSearchResult).filter(NodeSearchResult.temp_node_id==temp_node_id).delete(synchronize_session='fetch')
            # logger.info(f"已删除临时节点 {temp_node_id} 下的旧数据")
        elif node_id:
            db.query(NodeSearchResult).filter(NodeSearchResult.node_id==node_id).delete(synchronize_session='fetch')
            # logger.info(f"已删除正式节点 {node_id} 下的旧数据")
            
        # 批量插入新的搜索结果
        start_time = time.time()
        
        if results:
            # 逐条插入以获取ID
            for result in results:
                if node_id:
                    search_result = NodeSearchResult(
                        title=result['title'],
                        source=result['source'],
                        url=result['url'],
                        publish_time=result['publish_time'],
                        content=result['content'],
                        is_selected=0,
                        userid=user_id,
                        temp_node_id=temp_node_id,
                        node_id=node_id
                    )
                else:
                    search_result = NodeSearchResult(
                        title=result['title'],
                        source=result['source'],
                        url=result['url'],
                        publish_time=result['publish_time'],
                        content=result['content'],
                        is_selected=0,
                        userid=user_id,
                        temp_node_id=temp_node_id
                    )
                db.add(search_result)
                db.flush()  # 刷新会话，获取ID但不提交
                # 将ID添加到原始结果字典中
                result['id'] = search_result.id
        
        # 记录搜索来源信息
        node_info = f"临时节点ID: {temp_node_id}" if temp_node_id else f"正式节点ID: {node_id}"
        logger.info(f"保存搜索结果到数据库，{node_info}，关键词: {search_query}, 来源类型: {source_type}, 来源列表: {source_list}")
        
        db.commit()
        elapsed_time = time.time() - start_time
        logger.info(f"成功保存 {len(results)} 条搜索结果到数据库，耗时: {elapsed_time:.3f}秒")
        return results
    except Exception as e:
        db.rollback()
        logger.error(f"保存搜索结果失败: {str(e)}", exc_info=True)
        # 不抛出异常，避免影响用户体验

def search_articles(data: Dict, user_id: int, db: Session) -> List[Dict]:
    """
    根据搜索条件获取文章列表，并保存到节点搜索结果表
    
    Args:
        data: 包含搜索条件的字典，包含以下字段：
            - search_query: 搜索关键词（必选）
            - source_type: 来源类别（必选）：'wechat'(微信), 'website'(网站), 'domestic'(国内搜索), 'foreign'(国外搜索)
            - source_list: 具体来源（可选）：可以是字符串或列表，如微信公众号名称列表或网站链接列表
            - filter_time: 时间过滤条件（可选）
            - filter_quantity: 返回结果数量（必选）
            - temp_node_id: 临时节点ID（temp_node_id和node_id必须提供一个）
            - node_id: 正式节点ID（temp_node_id和node_id必须提供一个）
        user_id: 用户ID，用于关联搜索结果（必选）
        
    Returns:
        List[Dict]: 文章列表，每个文章包含以下字段：
            - id: 数据库中的记录ID
            - title: 文章标题
            - source: 文章来源
            - url: 文章链接
            - publish_time: 发布时间
            - content: 文章内容
            
    Raises:
        ValueError: 当搜索参数无效时
    """
    try:
        # 装饰器已验证必填字段，这里直接获取参数值
        search_query = data['search_query']
        source_type = data['source_type'].lower()
        source_list = data.get('source_list', [])
        filter_time = get_relative_date(data.get('filter_time', '今天'))
        filter_quantity = int(data['filter_quantity'])
        # 获取节点ID（临时节点ID或正式节点ID必须提供一个）
        temp_node_id = data.get('temp_node_id')
        node_id = data.get('node_id')
       
  
        # 过滤空字符串和去重
        source_list = list(set([s.strip() for s in source_list if s and s.strip()]))

        result_list = []
        
        # 根据来源类型选择不同的搜索方式
        if source_type == 'wechat':
            # 处理微信公众号来源
            if source_list:
                # 计算每个公众号应获取的文章数量
                per_source_quantity = distribute_quantity(filter_quantity, len(source_list))
                logger.info(f"每个公众号获取的文章数量: {per_source_quantity}")
                
                for i, wx_name in enumerate(source_list):
                    try:
                        # print(wx_name, filter_time, per_source_quantity[i])
                        article_list = get_weixin_articles_with_timeout(wx_name, filter_time, per_source_quantity[i])
                        wx_list = [{
                            'title': item['title'],
                            'source': item['author'],
                            'url': item['url'],
                            'publish_time': item['publish_time'],
                            'content': item['content']
                        } for item in article_list]
                        result_list.extend(wx_list)
                        logger.info(f"从公众号 '{wx_name}' 获取到 {len(wx_list)} 篇文章")
                    except TimeoutError:
                        logger.error(f"获取微信公众号 '{wx_name}' 文章超时")
                    except Exception as e:
                        logger.error(f"获取微信公众号 '{wx_name}' 文章出错: {str(e)}", exc_info=True)
            else:
                logger.warning("微信搜索需要指定公众号名称")
                
        elif source_type == 'website':
            # 处理网站来源
            if source_list:
                # 计算每个网站应获取的文章数量
                per_source_quantity = distribute_quantity(filter_quantity, len(source_list))
                logger.info(f"每个网站获取的文章数量: {per_source_quantity}")
                
                for i, site_url in enumerate(source_list):
                    try:
                        if 'https://news.bjx.com.cn' in site_url:
                            article_list = get_bjx_news_with_timeout('综合', search_query, filter_time, per_source_quantity[i])
                            bjx_list = [{
                                'title': item['title'],
                                'source': item['source'],
                                'url': item['url'],
                                'publish_time': item['detail_time'],
                                'content': item['content']
                            } for item in article_list]
                            result_list.extend(bjx_list)
                            logger.info(f"从网站 '{site_url}' 获取到 {len(bjx_list)} 篇文章")
                        else:
                            logger.warning(f"不支持的网站来源: {site_url}")
                    except TimeoutError:
                        logger.error(f"获取网站 '{site_url}' 文章超时")
                    except Exception as e:
                        logger.error(f"获取网站 '{site_url}' 文章出错: {str(e)}", exc_info=True)
            else:
                logger.warning("网站搜索需要指定网站URL")
                
        elif source_type == 'foreign':
            # 使用谷歌API进行国外搜索
            try:
                web_list = get_google_api_with_timeout(search_query, filter_time, filter_quantity)
                now_time = datetime.now().strftime("%Y-%m-%d %H:%M")
                foreign_list = [{
                    'title': item['title'], 
                    'source': item['site'], 
                    'url': item['url'], 
                    'publish_time': now_time, 
                    'content': item['detail_content'] or item['content']
                } for item in web_list]
                result_list.extend(foreign_list)
                logger.info(f"获取到 {len(foreign_list)} 条国外搜索结果")
            except TimeoutError:
                logger.error("谷歌API国外搜索超时")
            except Exception as e:
                logger.error(f"谷歌API国外搜索出错: {str(e)}", exc_info=True)
                
        elif source_type == 'domestic':
            # 使用AI模型进行国内搜索
            try:
                ai_results = get_ai_model_query_results_with_timeout(search_query, filter_quantity)
                domestic_list = [{
                    'title': item['title'],
                    'source': item['source'],
                    'url': item['url'],
                    'publish_time': item['publish_time'],
                    'content': item['content']
                } for item in ai_results]
                result_list.extend(domestic_list)
                logger.info(f"获取到 {len(domestic_list)} 条国内搜索结果")
            except TimeoutError:
                logger.error("AI模型国内搜索超时")
            except Exception as e:
                logger.error(f"AI模型国内搜索出错: {str(e)}", exc_info=True)
                
        else:
            raise ValueError(f"不支持的来源类型: {source_type}")
            
        # 保存搜索结果到数据库
        # 由于 save_search_results 内部会进行 flush 和 commit，这里不需要额外的 commit
        saved_results = save_search_results(result_list, user_id, search_query, source_type, source_list, db, temp_node_id, node_id)

        return saved_results

    except ValueError as e:
        logger.error(f"搜索参数验证失败: {str(e)}", exc_info=True)
        raise
    except Exception as e:
        logger.error(f"搜索文章失败: {str(e)}", exc_info=True)
        raise RuntimeError(f"搜索文章失败: {str(e)}")

def distribute_quantity(total: int, num_sources: int) -> List[int]:
    """
    将总数量平均分配给多个来源，不足的分配给前几个来源
    """
    if num_sources <= 0:
        return []
    
    base_quantity = total // num_sources
    remainder = total % num_sources
    
    quantities = [base_quantity] * num_sources
    for i in range(remainder):
        quantities[i] += 1
        
    return quantities 