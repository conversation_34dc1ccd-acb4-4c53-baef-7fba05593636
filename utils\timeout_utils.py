#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
超时处理工具模块
"""
import signal
from functools import wraps
from utils.logger import setup_logger

logger = setup_logger('utils.timeout')

def timeout(seconds, error_message="API调用超时"):
    """
    为函数添加超时机制的装饰器
    
    Args:
        seconds: 超时秒数
        error_message: 超时错误消息
        
    Returns:
        包装后的函数
    """
    def decorator(func):
        def _handle_timeout(signum, frame):
            raise TimeoutError(error_message)
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 仅在UNIX系统上使用signal
            try:
                # 设置信号处理器
                signal.signal(signal.SIGALRM, _handle_timeout)
                signal.alarm(seconds)
                try:
                    result = func(*args, **kwargs)
                finally:
                    # 取消警报
                    signal.alarm(0)
                return result
            except (AttributeError, ValueError):
                # Windows系统不支持SIGALRM，使用其他方法
                logger.warning("当前系统不支持signal超时机制，使用requests超时参数")
                # 在这里我们依赖被调用函数自己的超时机制
                return func(*args, **kwargs)
        return wrapper
    return decorator 