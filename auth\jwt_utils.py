from datetime import datetime, timedelta
import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any, Optional
from functools import wraps
# from utils.response import unauthorized, internal_error
from utils.logger import setup_logger
from utils.error_handler import APIError
from models import User, get_db
from sqlalchemy.orm import Session

logger = setup_logger('auth.jwt_utils')

class JWTManager:
    """
    JWT管理器
    """
    def __init__(self, secret_key: str, algorithm: str = 'HS256', token_expires: int = 12 * 3600):
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.token_expires = token_expires  # 令牌有效期12小时

    # def init_app(self, app):
    #     """
    #     初始化JWT管理器
    #     
    #     Args:
    #         app: Flask应用实例
    #     """
    #     self.secret_key = app.config['SECRET_KEY']

    def generate_token(self, user_id: int, username: str) -> str:
        """
        生成JWT token
        """
        payload = {
            'user_id': user_id,
            'username': username,
            'exp': datetime.now() + timedelta(seconds=self.token_expires),
            'iat': datetime.now()  # 签发时间
        }
        
        logger.debug(f"为用户生成token: user_id={user_id}, username={username}")
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        验证JWT token
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("Token已过期")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"无效的Token: {str(e)}")
            return None

    def get_current_user(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer()), db: Session = Depends(get_db)):
        token = credentials.credentials
        payload = self.verify_token(token)
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效或已过期的token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        user = db.query(User).filter(User.id == payload['user_id']).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        return payload

# 从config中获取SECRET_KEY
from config import Config
jwt_manager = JWTManager(secret_key=Config.SECRET_KEY)

# Removed token_required decorator as it's replaced by FastAPI's Depends and get_current_user

# 创建JWT管理器实例
# jwt_manager = JWTManager() 