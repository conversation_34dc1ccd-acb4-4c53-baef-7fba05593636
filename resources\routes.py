from fastapi import APIRouter, Request, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session
from auth.jwt_utils import jwt_manager
from resources.services import process_urls, upload_file_and_process, delete_resource
from utils.logger import setup_logger
from utils.error_handler import APIError
from models import get_db
from pydantic import BaseModel
from typing import List, Optional

logger = setup_logger('resources.routes')

resource_router = APIRouter(prefix="/api", tags=["resources"])

class URLUploadRequest(BaseModel):
    urls: List[str]
    temp_node_id: Optional[str] = None
    node_id: Optional[int] = None

@resource_router.post("/resource/url")
async def handle_upload_url(
    url_data: URLUploadRequest,
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    处理URL上传请求
    
    Args:
        url_data: 包含URLs信息的JSON数据
            - urls: URL列表（必选）
            - temp_node_id: 临时节点ID（temp_node_id和node_id必须提供一个），用于关联资源
            - node_id: 正式节点ID（temp_node_id和node_id必须提供一个），用于关联资源
    
    Returns:
        dict: 包含处理结果的JSON响应
        
    Raises:
        APIError: 当处理过程中发生错误时抛出
    """
    try:
        user_id = current_user['user_id']
        
        # 将整个data和user_id传给service方法，在service中处理参数验证
        result = process_urls(url_data.model_dump(exclude_unset=True), user_id, db)
        
        return {"code": 200, "message": "URL处理成功", "data": result}
    except APIError as e:
        logger.error(f"URL处理失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"URL处理失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"URL处理失败: {str(e)}"
        )

class FileUploadForm(BaseModel):
    file_name: str
    temp_node_id: Optional[str] = None
    node_id: Optional[int] = None

@resource_router.post("/resource/file")
async def handle_upload_file(
    file_stream: UploadFile = File(...),
    file_name: str = Form(...),
    temp_node_id: Optional[str] = Form(None),
    node_id: Optional[int] = Form(None),
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    处理文件上传请求
    
    Args:
        file_stream: 上传的文件流（必选）
        file_name: 文件名（必选）
        temp_node_id: 临时节点ID（temp_node_id和node_id必须提供一个），用于关联资源
        node_id: 正式节点ID（temp_node_id和node_id必须提供一个），用于关联资源
    
    Returns:
        dict: 包含处理结果的JSON响应，包括文件名、文件类型、字数等信息
        
    Raises:
        APIError: 当处理过程中发生错误时抛出
    """
    try:
        # 获取用户ID
        user_id = current_user['user_id']
        
        # 准备数据字典传递给service
        file_data = {
            "file_name": file_name,
            "temp_node_id": temp_node_id,
            "node_id": node_id
        }

        # 在service中处理参数获取和验证
        result = upload_file_and_process(file_stream, file_data, user_id, db)
        
        return {"code": 200, "message": "文件处理成功", "data": result}
    except APIError as e:
        logger.error(f"文件处理失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"文件处理失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件处理失败: {str(e)}"
        )

@resource_router.delete("/resource/{resource_id}")
async def handle_delete_resource(
    resource_id: int,
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    删除指定资源
    
    Args:
        resource_id: 资源ID
    
    Returns:
        dict: 包含删除结果的JSON响应
        
    Raises:
        APIError: 当处理过程中发生错误时抛出
    """
    try:
        # 获取用户ID和资源ID
        user_id = current_user['user_id']
        
        # 调用服务方法删除资源
        result = delete_resource(resource_id, user_id, db)
        
        return {"code": 200, "message": "删除成功", "data": result}
    except APIError as e:
        logger.error(f"删除资源失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"删除资源失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除资源失败: {str(e)}"
        ) 