from fastapi import APIRouter, Request, Depends, HTTPException, status
from sqlalchemy.orm import Session
from auth.jwt_utils import jwt_manager
from agents.services import create_or_update_agent, get_agents, delete_agent, run_agent
from utils.logger import setup_logger
from utils.error_handler import APIError
from models import get_db
from pydantic import BaseModel, Field
from typing import Optional

logger = setup_logger('agents.routes')

agent_router = APIRouter(prefix="/api", tags=["agents"])

class AgentCreateUpdate(BaseModel):
    name: str
    description: str
    category: str
    agent_id: Optional[int] = Field(None, description="Agent ID (optional, required for update)")

@agent_router.post("/agent")
async def handle_create_or_update_agent(
    agent_data: AgentCreateUpdate,
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建或更新Agent的路由处理函数
    """
    try:
        user_id = current_user['user_id']
        result = create_or_update_agent(agent_data.model_dump(), user_id, db)
        return {"code": 200, "message": "操作成功", "data": result}
        
    except APIError as e:
        logger.error(f"创建/更新Agent失败: {str(e)}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"创建/更新Agent失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建/更新Agent失败: {str(e)}"
        )

@agent_router.get("/agents")
async def handle_get_agents(
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取用户所有Agent的路由处理函数
    """
    try:
        user_id = current_user['user_id']
        logger.info(f"获取Agent列表请求: user_id={user_id}")
        
        agents = get_agents(user_id, db)
        return {"code": 200, "message": "获取成功", "data": agents}
        
    except APIError as e:
        logger.error(f"获取Agent列表失败: {str(e)}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"获取Agent列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Agent列表失败: {str(e)}"
        )

@agent_router.delete("/agent/{agent_id}")
async def handle_delete_agent(
    agent_id: int,
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    删除指定Agent的路由处理函数
    """
    try:
        user_id = current_user['user_id']
        
        result = delete_agent(agent_id, user_id, db)
        return {"code": 200, "message": "删除成功", "data": result}
        
    except APIError as e:
        logger.error(f"删除Agent失败: {str(e)}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"删除Agent失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除Agent失败: {str(e)}"
        )

class AgentRun(BaseModel):
    agent_id: int

@agent_router.post("/agent/run")
async def handle_run_agent(
    agent_run_data: AgentRun,
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    运行智能体生成内容的路由处理函数
    """
    try:
        user_id = current_user['user_id']
        agent_id = agent_run_data.agent_id
        logger.info(f"处理智能体运行请求: user_id={user_id}, agent_id={agent_id}")
        
        result = run_agent(agent_id, user_id, db)
        
        logger.info("智能体运行成功")
        return {"code": 200, "message": "运行成功", "data": result}
    except APIError as e:
        logger.error(f"运行智能体失败: {str(e)}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"运行智能体失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"运行智能体失败: {str(e)}"
        )