import json
from tencentcloud.common.common_client import CommonClient
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from datetime import datetime
from utils.url_processor import extract_text_from_url
import time
import random

# 密钥
secret_id = 'AKIDGYlmqoYAaoqhLsDPQEoDVhgS0LUQ82nQ'  # 填充客户的 AK Sk 即可
secret_key = 'RrCML6jEi2A7qLcgXBfw1ta5PXSAew3y'  # 填充客户的 AK Sk 即可

def get_ai_model_query_results(query, date_filter, limit):
	# print('sogou',query,date_filter,limit)
	cred = credential.Credential(secret_id, secret_key)  # 填充客户的 AK Sk 即可
	httpProfile = HttpProfile()
	httpProfile.endpoint = "tms.tencentcloudapi.com"
	clientProfile = ClientProfile()
	clientProfile.httpProfile = httpProfile
	params = {'Query':query}
	common_client = CommonClient("tms", "2020-12-29", cred, "", profile=clientProfile)
	rsp_json = common_client.call_json("SearchPro", params)
	request_id = rsp_json['Response']['RequestId']
	uuid = rsp_json['Response']['Uuid']
	pages = rsp_json['Response']['Pages']
	# print(request_id, uuid)
	page_list = []
	for page in pages:
		page = eval(page)
		# dict_keys(['passage', 'score', 'date', 'title', 'url', 'site', 'images', 'favicon'])
		# print(page['passage'])
		# print(page['score']) # 可信率
		# print(page['date']) # 信息发布时间 '2025-04-16 06:48:13'
		# print(page['title']) # 标题
		# print(page['url']) # 链接
		# print(page['site']) # 来源网站
		
		article_date = datetime.strptime(page['date'], '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
		if article_date < date_filter:
			continue
		
		# 准备基本信息
		article_info = {
			'title': page['title'],
			'date': page['date'],
			'content': page['passage'],
			'score': page['score'],
			'url': page['url'],
			'site': page['site'],
			'detail_content': ''  # 默认为空字符串
		}
		
		# 尝试爬取详细内容
		try:
			# 添加随机延时，避免请求过于频繁
			time.sleep(random.uniform(0, 1))
			
			# 使用extract_text_from_url爬取详细内容
			detail_content = extract_text_from_url(page['url'])
			
			# 如果成功获取内容，则添加到结果中
			if detail_content:
				article_info['detail_content'] = detail_content
			else:
				print(f"从URL获取的内容为空: {page['url']}")
		except Exception as e:
			print(f"爬取详细内容失败: {page['url']} - {str(e)}")
			# 即使爬取失败，仍然添加其他信息
		
		page_list.append(article_info)
		
		# 如果已经达到限制数量，则停止处理
		if len(page_list) >= limit:
			break
	return page_list

if __name__ == '__main__':
	query = 'ChatGPT是什么'
	date_filter = '2025-04-01'
	page_list = get_ai_model_query_results(query, date_filter, 3)
	print(len(page_list))
	for page in page_list:
		print(f"标题: {page['title']}")
		print(f"URL: {page['url']}")
		print(f"详细内容长度: {len(page['detail_content'])}")
		print(page['content'])
		print(page['detail_content'])
		print("-" * 50)