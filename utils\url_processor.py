import requests
from bs4 import BeautifulSoup
from typing import Dict, Any
from urllib.parse import urlparse
import trafilatura
import random
import time
from fake_useragent import UserAgent
from utils.logger import setup_logger

logger = setup_logger('utils.url_processor')

def is_valid_url(url: str) -> bool:
    """
    检查URL是否有效。
    
    Args:
        url: 要检查的URL
        
    Returns:
        如果URL格式有效返回True，否则返回False
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False

def get_random_user_agent():
    """
    获取随机User-Agent
    
    如果fake_useragent可用，使用它生成随机UA
    否则从预定义列表中随机选择
    
    Returns:
        随机的User-Agent字符串
    """
    try:
        ua = UserAgent()
        return ua.random
    except:
        # 预定义User-Agent列表作为备选
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 Edg/121.0.0.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36",
        ]
        return random.choice(user_agents)

def extract_text_from_url(url: str) -> str:
    """
    从网页提取文本内容，使用trafilatura库进行内容提取。
    
    Args:
        url: 要提取文本的网页URL
        
    Returns:
        提取的文本内容字符串
        
    Raises:
        ValueError: 如果URL无效
        requests.exceptions.RequestException: 如果请求失败
        Exception: 解析过程中的其他错误
    """
    # 验证URL格式
    if not is_valid_url(url):
        raise ValueError(f"无效的URL格式: {url}")
    
    # # 随机延时，模拟人类行为
    # time.sleep(random.uniform(1, 3))
    
    # 设置请求头，使用随机User-Agent
    headers = {
        "User-Agent": get_random_user_agent(),
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Referer": "https://www.google.com",  # 添加referer
        "Connection": "keep-alive"
    }
    
    # 设置代理（如果需要）
    proxies = {
        "http": "http://127.0.0.1:7890",
        "https": "http://127.0.0.1:7890"
    }
    # 如果有代理，可以在这里设置
    # proxies = {
    #     "http": "http://proxy.example.com:8080",
    #     "https": "https://proxy.example.com:8080"
    # }

    try:
        # 首先尝试使用trafilatura直接从URL提取
        downloaded = trafilatura.fetch_url(url)
        html_content = None
        
        # 如果trafilatura下载成功，使用下载的内容
        if downloaded:
            html_content = downloaded
        else:
            # 如果trafilatura下载失败，使用requests作为后备
            response = requests.get(url, headers=headers, proxies=proxies, timeout=15)
            response.encoding = 'utf-8'
            response.raise_for_status()
            html_content = response.text
        
        # 使用trafilatura从HTML提取文本
        text = trafilatura.extract(html_content, 
                                 include_comments=False,
                                 include_tables=True, 
                                 favor_precision=True,
                                 output_format='txt')
        
        # 如果trafilatura提取失败，使用BeautifulSoup作为后备方案
        if not text:
            soup = BeautifulSoup(html_content, "html.parser")
            # 移除不需要的元素
            for element in soup(["script", "style", "iframe", "nav", "footer"]):
                element.extract()
            # 提取并清理文本
            text = soup.get_text(separator=" ", strip=True)
            text = " ".join(text.split())
        
        # 检查提取的文本是否为空
        if not text or not text.strip():
            logger.warning(f"从URL提取的内容为空: {url}")
            # 不抛异常，直接返回空字符串
            return ""
            
        return text
    except requests.exceptions.RequestException as e:
        # 将请求错误转发出去，不吞掉异常
        print(f"请求错误 {url}: {str(e)}")
        raise
    except Exception as e:
        # 将其他错误也转发出去
        print(f"提取文本时出错 {url}: {str(e)}")
        raise

def process_url(url: str) -> Dict[str, Any]:
    """
    处理单个URL，提取文本内容和字数。
    
    Args:
        url: 要处理的URL
        
    Returns:
        包含URL、文本内容和字数的字典
        
    Raises:
        ValueError: 如果URL格式无效或内容为空
        requests.exceptions.RequestException: 如果网页请求失败
        Exception: 处理过程中的其他错误
    """
    # 验证URL格式
    if not is_valid_url(url):
        raise ValueError(f"无效的URL格式: {url}")
    
    # 提取文本 - 直接让底层方法抛出异常
    text = extract_text_from_url(url)
    
    # 计算字数
    word_count = len(text)
    
    # 返回结果
    return {
        "url": url,
        "content": text,
        "word_count": word_count,
        "success": True
    } 