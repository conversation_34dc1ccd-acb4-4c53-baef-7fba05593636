# -*- coding: utf-8 -*-
"""
微信公众号爬虫模块
用于爬取指定公众号的文章列表和详情
"""

import ast
import itertools
import requests
import re
import time
import random
import redis
import json
from bs4 import BeautifulSoup
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from config import Config

# 添加公众号fakeid映射表
# 预设的公众号名称到fakeid的映射
WECHAT_FAKEID_MAP = {
	"龙船风电网": "Mzg5NjQ2Mzk4NA=="
	# 可以添加更多的映射
}

@dataclass
class CrawlerConfig:
	"""爬虫配置类"""
	biz_name: str      # 公众号名称
	cookie: str        # 登录cookie
	token: str         # 访问令牌
	fingerprint: str   # 指纹信息
	per_page: int = 5  # 每页文章数量

class WeixinCrawler:
	"""微信公众号爬虫类
	
	主要功能:
	1. 获取公众号的fakeid
	2. 获取文章列表
	3. 获取文章详情
	4. 将文章数据写入数据库
	
	Attributes:
		url (str): 文章列表URL
		search_url (str): 公众号搜索URL
		headers (Dict[str, str]): 请求头
		token (str): 访问令牌
		fingerprint (str): 指纹信息
		fakeid (str): 公众号唯一标识
		config (CrawlerConfig): 爬虫配置
	"""

	def __init__(self, config: CrawlerConfig):
		"""初始化爬虫
		
		Args:
			config: 爬虫配置
			db_config: 数据库配置
			is_write: 是否写入数据库
		"""
		self.url = 'https://mp.weixin.qq.com/cgi-bin/appmsgpublish'
		self.search_url = 'https://mp.weixin.qq.com/cgi-bin/searchbiz'
		self.headers = {
			'Cookie': config.cookie,
			'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0',
		}
		self.token = config.token
		self.fingerprint = config.fingerprint
		self.config = config
		
		# 初始化会话对象
		self.session = requests.Session()
		
		# 将请求头应用到会话对象
		self.session.headers.update(self.headers)
		
		# 直接从映射表获取fakeid
		self.fakeid = WECHAT_FAKEID_MAP.get(config.biz_name)
		
		# 如果映射表中没有，则调用API获取
		if not self.fakeid:
			self.fakeid = self.get_fakeid(config.biz_name)

		if not self.fakeid:
			raise ValueError(f"无法获取公众号 {config.biz_name} 的fakeid")

	def __del__(self):
		"""析构函数，确保会话关闭"""
		if hasattr(self, 'session'):
			self.session.close()

	# ========== 核心爬取方法 ==========
	def get_content_list(self, target_date: str = None, limit: int = None) -> List[Dict]:
		"""获取文章列表
		
		流程:
		1. 循环获取每页文章
		2. 解析文章列表数据
		3. 根据目标日期筛选文章
		4. 获取文章详情
		5. 写入数据库(如果启用)
		
		Args:
			target_date: 目标日期,格式为 'YYYY-MM-DD',不传则爬取所有文章
			limit: 最大爬取文章数量,不传则爬取所有文章
			
		Returns:
			List[Dict]: 文章列表，数量不超过limit
		"""
		content_list = []
		should_break = False
		remaining_limit = limit  # 记录还需要获取的文章数量
		start_time = time.time()  # 记录开始时间
		
		for i in itertools.count(start=0):
			# 如果已达到limit限制，直接退出循环
			if limit is not None and len(content_list) >= limit:
				break
				
			try:
				# 构建请求参数
				data = {
					'sub': 'list',
					'search_field': 'null',
					'begin': i * self.config.per_page,
					'count': self.config.per_page,
					'query': '',
					'fakeid': self.fakeid,
					'type': '101_1',
					'free_publish_type': 1,
					'sub_action': 'list_ex',
					'fingerprint': self.fingerprint,
					'token': self.token,
					'lang': 'zh_CN',
					'f': 'json',
					'ajax': 1
				}
				
				# 使用会话对象发送请求获取文章列表
				response = self.session.get(self.url, params=data)
				response.raise_for_status()
				content_json = response.json()
				
				if not content_json or 'publish_page' not in content_json:
					print(f"未获取到文章列表或格式异常，页码: {i}")
					break
				
				# 解析文章列表数据
				try:
					publish_page = ast.literal_eval(content_json['publish_page'].replace('true', 'True').replace('false', 'False'))
					
					# 使用列表表达式处理文章列表
					articles = [
						{
							'title': a['title'],
							'url': a['link'].replace('\\/', '/'),
							'publish_date': datetime.fromtimestamp(int(a.get('create_time', 0))).strftime('%Y-%m-%d')
						}
						for p in publish_page.get('publish_list', [])
						for a in ast.literal_eval(p.get('publish_info', '[]')).get('appmsgex', [])
						if 'title' in a and 'link' in a
						and (
							not target_date 
							or not a.get('create_time')
							or datetime.fromtimestamp(int(a['create_time'])).strftime('%Y-%m-%d') >= target_date
						)
					]
				except (ValueError, KeyError, AttributeError) as e:
					print(f"解析文章列表出错: {str(e)}")
					articles = []
				
				if not articles:
					# print(f"第{i+1}页没有找到符合条件的文章")
					break
					
				# print(f"第{i+1}页找到 {len(articles)} 篇文章")
				
				# 如果当前页没有符合条件的文章,说明需要停止爬取
				if target_date and len(articles) < len([
					a for p in publish_page.get('publish_list', [])
					for a in ast.literal_eval(p.get('publish_info', '[]')).get('appmsgex', [])
					if 'title' in a and 'link' in a
				]):
					should_break = True
					print(f"由于日期筛选条件，停止爬取")

				# 如果有limit限制，只取所需数量的文章
				if limit is not None:
					remaining_limit = limit - len(content_list)
					if len(articles) > remaining_limit:
						# print(f"限制文章数量，从 {len(articles)} 减少到 {remaining_limit}")
						articles = articles[:remaining_limit]

				# 获取文章详情
				detailed_articles = []
				for idx, article in enumerate(articles):
					# 实时判断是否已达到limit
					if limit is not None and len(content_list) + len(detailed_articles) >= limit:
						print(f"已达到限制数量 {limit}，停止获取文章详情")
						break
						
					# print(f"正在获取第 {idx+1}/{len(articles)} 篇文章详情: {article['title']}")
					article_details = self.get_article(article['url'])
					if article_details:  # 只添加成功获取详情的文章
						article.update(article_details)
						detailed_articles.append(article)
					else:
						print(f"无法获取文章详情: {article['title']}")

				# 添加获取到详情的文章
				content_list.extend(detailed_articles)
				# print(f"当前已获取 {len(content_list)}/{limit if limit else '不限'} 篇文章")
				
				# 判断是否需要退出循环
				if should_break or (limit is not None and len(content_list) >= limit):
					break
				
			except requests.RequestException as e:
				print(f"获取第{i+1}页文章列表请求失败: {str(e)}")
				break
			except Exception as e:
				print(f"获取第{i+1}页文章列表时发生未知错误: {str(e)}")
				break
		
		elapsed_time = time.time() - start_time
		# print(f"爬取完成，共获取 {len(content_list)} 篇文章，耗时: {elapsed_time:.2f}秒")
		
		# 最后确保返回的数量严格符合limit限制		
		if limit is not None and len(content_list) > limit:
			return content_list[:limit]
			
		return content_list

	def get_article(self, article_url):
		"""获取文章详情
		
		Args:
			article_url: 文章链接
			
		Returns:
			dict: 包含文章内容、发布时间、作者、封面图的字典，发生错误时返回None
		"""

		for retry in range(3):
			try:
				resp = self.session.get(article_url, timeout=10)
				resp.raise_for_status()
				resp.encoding = 'utf-8'
				article_html = resp.text

				if self.contains_captcha_script(article_html):
					time.sleep(random.uniform(2, 5))
					continue

				soup = BeautifulSoup(article_html, 'lxml')
				if soup.find('div', {'class': 'weui-msg__title'}):
					return {}

				author = soup.find('span', id='profileBt')
				if not author:
					return {}
				author = author.text.strip()

				publish_time = self.extract_publish_time(article_html)
				if not publish_time:
					return {}

				ip_address = self.extract_ip_wording(article_html)

				content = soup.find(id='js_content')
				if not content:
					return {}
				content = content.text.strip()

				# 提取封面图
				cover_image = ""
				cover_img_elem = soup.find('img', {'class': 'rich_pages'})
				if cover_img_elem and 'data-src' in cover_img_elem.attrs:
					cover_image = cover_img_elem['data-src']

				# 构建结果
				result = {
					'content': content,
					'publish_time': publish_time,
					'author': author,
					'ip_address': ip_address,
					'cover_image': cover_image
				}

				return result
				
			except (requests.RequestException, Exception) as e:
				print(f"获取文章详情失败 (尝试 {retry+1}/3): {str(e)}")
				time.sleep(random.uniform(2, 5))
				
		print(f"获取文章详情失败，已达到最大重试次数")
		return {}

	# ========== 辅助方法 ==========
	def get_fakeid(self, biz_name: str) -> str:
		"""获取公众号的fakeid
		
		Args:
			biz_name: 公众号名称
			
		Returns:
			str: 公众号的fakeid
		"""
		try:
			params = {
				'action': 'search_biz',
				'begin': 0,
				'count': 5,
				'query': biz_name,
				'fingerprint': self.fingerprint,
				'token': self.token,
				'lang': 'zh_CN',
				'f': 'json',
				'ajax': 1
			}

			response = requests.get(self.search_url, headers=self.headers, params=params)
			response.raise_for_status()
			data = response.json()

			if not data or 'list' not in data:
				return ''

			for biz in data['list']:
				if biz.get('nickname') == biz_name:
					return biz.get('fakeid', '')

			return ''
			
		except requests.RequestException as e:
			return ''
		except Exception as e:
			return ''

	def extract_ip_wording(self, html: str) -> Optional[str]:
		"""提取IP属地信息"""
		pattern = r"window\.ip_wording\s*=\s*({.*?});"
		match = re.search(pattern, html, re.DOTALL)
		if not match:
			return None

		province_pattern = r"provinceName\s*:\s*['\"](.*?)['\"]"
		province_match = re.search(province_pattern, match.group(1))
		return province_match.group(1) if province_match else None

	def extract_publish_time(self, html: str) -> Optional[str]:
		"""提取发布时间"""
		create_time_match = re.search(r"var createTime\s*=\s*'(.*?)';", html)
		if create_time_match:
			return create_time_match.group(1)

		timestamp_match = re.search(r"var oriCreateTime\s*=\s*'(\d+)';", html)
		if timestamp_match:
			return datetime.fromtimestamp(int(timestamp_match.group(1))).strftime('%Y-%m-%d %H:%M')

		fallback_match = re.search(r"var svrDate\s*=\s*'(\d+)';", html)
		if fallback_match:
			return datetime.fromtimestamp(int(fallback_match.group(1))).strftime('%Y-%m-%d %H:%M')

		return None

	def contains_captcha_script(self, html: str) -> bool:
		"""检查是否包含验证码"""
		soup = BeautifulSoup(html, 'html.parser')
		captcha_script = soup.find('script', src=lambda x: x and 'TCaptcha.js' in x)
		return bool(captcha_script)

# ========== 配置和Redis工具函数 ==========
def get_redis_client():
	"""获取Redis客户端连接"""
	try:
		client = redis.Redis(
			host=Config.REDIS_HOST,
			port=Config.REDIS_PORT,
			db=Config.REDIS_DB,
			password=Config.REDIS_PASSWORD,
			decode_responses=True  # 将二进制响应解码为字符串
		)
		# 测试连接
		client.ping()
		return client
	except redis.ConnectionError as e:
		print(f"Redis连接失败: {str(e)}")
		return None
	except Exception as e:
		print(f"Redis连接出错: {str(e)}")
		return None

def get_weixin_config() -> Tuple[Optional[str], Optional[str], Optional[str]]:
	"""
	从Redis获取微信爬虫配置
	
	Returns:
		tuple: (cookie, token, fingerprint)，获取失败则返回None
	"""
	try:
		client = get_redis_client()
		if not client:
			return None, None, None
			
		config_json = client.get(Config.WEIXIN_CONFIG_KEY)
		
		if not config_json:
			print("Redis中未找到微信爬虫配置")
			return None, None, None
			
		config = json.loads(config_json)
		return (
			config.get('cookie', ''),
			config.get('token', ''),
			config.get('fingerprint', '')
		)
	except Exception as e:
		print(f"获取微信爬虫配置失败: {str(e)}")
		return None, None, None

def get_weixin_articls(biz_name, target_date, limit):
	"""获取微信公众号文章
	
	Args:
		biz_name: 公众号名称
		target_date: 目标日期,格式为 'YYYY-MM-DD',不传则爬取所有文章
		limit: 最大爬取文章数量,不传则爬取所有文章
	"""
	try:
		# print('微信',biz_name, target_date, limit)
		cookie, token, fingerprint = get_weixin_config()
		print(cookie, token, fingerprint)
		config = CrawlerConfig(
			biz_name=biz_name,
			cookie=cookie,
			token=token,
			fingerprint=fingerprint
		)
		crawler = WeixinCrawler(config)
		content_list = crawler.get_content_list(target_date, limit)
		return content_list
	except Exception as e:
		print(f"程序运行失败: {str(e)}")
		raise

if __name__ == '__main__':
	biz_name = '龙船风电网'
	target_date = '2025-04-17'  # 示例:只爬取2024年4月14日之后的文章
	content_list = get_weixin_articls(biz_name, target_date, 1)
	print(len(content_list), content_list)

