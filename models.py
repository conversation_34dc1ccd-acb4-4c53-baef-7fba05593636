from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from werkzeug.security import generate_password_hash, check_password_hash
# from sqlalchemy import Text

# 创建SQLAlchemy实例
# db = SQLAlchemy()

# 从config中获取数据库URI
from config import Config

# 定义Base
Base = declarative_base()

# 配置数据库连接
SQLALCHEMY_DATABASE_URI = Config.SQLALCHEMY_DATABASE_URI
engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_pre_ping=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    phone = Column(String(20))
    password = Column(String(255), nullable=False)
    nickname = Column(String(50))
    avatar = Column(String(255))
    status = Column(Integer, default=1)
    last_login = Column(DateTime)
    last_login_ip = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    def set_password(self, password):
        self.password = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password, password)

class Agent(Base):
    __tablename__ = 'agents'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)  # 一般介绍文本，使用普通TEXT
    category = Column(String(255), nullable=False)
    generated_content = Column(Text)  # 生成内容，使用普通TEXT
    userid = Column(Integer, nullable=False)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class Node(Base):
    __tablename__ = 'nodes'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    agent_id = Column(Integer, ForeignKey('agents.id', ondelete='CASCADE'), nullable=False)
    node_name = Column(String(255), nullable=False)
    content_style = Column(String(100), nullable=False)
    text_content = Column(Text)  # 节点文本内容，使用普通TEXT
    summary_content = Column(Text)  # 节点总结内容，使用普通TEXT
    # TODO: 临时新增字段，后续需要整合
    text_type = Column(Integer, default=0)  # 文本类型：0-普通文本，1-特殊文本
    profession = Column(String(255))  # 职业字段
    # 搜索相关字段
    search_query = Column(String(1024))
    source_type = Column(String(100))   # 来源类别：wechat, website, domestic, foreign
    source_list = Column(String(512))   # 具体来源：微信公众号名称或网站链接
    filter_time = Column(String(100))
    filter_quantity = Column(Integer)
    is_referenced = Column(Integer, default=0)
    userid = Column(Integer, nullable=False)
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    

class NodeSearchResult(Base):
    __tablename__ = 'node_search_results'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    title = Column(String(255), nullable=False)
    source = Column(String(255), nullable=False)
    url = Column(String(1024))
    publish_time = Column(String(100))  # 改为String类型，因为来源的日期格式可能不统一
    content = Column(Text(length=16777215))  # 使用MEDIUMTEXT类型 (16MB)
    is_selected = Column(Integer, default=0)  # 0: 未选中, 1: 已选中
    userid = Column(Integer, nullable=False, index=True)
    temp_node_id = Column(String(255), index=True)    # 临时节点ID（UUID）
    node_id = Column(Integer, index=True)             # 正式节点ID
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 定义复合索引
    __table_args__ = (
        Index('idx_userid_temp_node_id', 'userid', 'temp_node_id'),  # 用户ID和临时节点ID的复合索引
        Index('idx_userid_node_id', 'userid', 'node_id'),           # 用户ID和正式节点ID的复合索引
        Index('idx_is_selected', 'is_selected'),                    # 选中状态索引
        Index('idx_source', 'source'),                              # 来源索引，用于过滤特定来源的结果
    )

class NodeResource(Base):
    __tablename__ = 'node_resources'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    resource_path = Column(String(255), nullable=False)
    name = Column(String(255), nullable=False)
    file_type = Column(String(100), nullable=False)
    content = Column(Text(length=16777215))  # 使用MEDIUMTEXT类型 (16MB)
    word_count = Column(Integer)
    upload_time = Column(DateTime)
    userid = Column(Integer, nullable=False)
    temp_node_id = Column(String(255))         # 临时节点ID（UUID）
    node_id = Column(Integer)                # 正式节点ID
    create_time = Column(DateTime, default=datetime.now)
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now)