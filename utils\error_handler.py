from fastapi import Request
from fastapi.responses import JSONResponse
from utils.logger import setup_logger

# 配置日志
logger = setup_logger('error_handler')

class APIError(Exception):
    """
    API错误基类
    
    Attributes:
        message (str): 错误消息
        status_code (int): HTTP状态码
        payload (dict): 额外的错误信息
    """
    def __init__(self, message: str, status_code: int = 400, payload: dict = None):
        super().__init__()
        self.message = message
        self.status_code = status_code
        self.payload = payload

    def to_dict(self):
        """
        将错误信息转换为字典格式
        
        Returns:
            dict: 包含错误信息的字典
        """
        return {
            'code': self.status_code,
            'message': self.message,
            'data': self.payload or {}
        }

async def api_error_handler(request: Request, exc: APIError):
    """
    处理API错误
    """
    logger.error(f"API Error: {exc.message}", exc_info=True)
    return JSONResponse(
        status_code=exc.status_code,
        content=exc.to_dict()
    )

async def http_exception_handler(request: Request, exc: Exception):
    """
    处理HTTP错误 (通用异常，包括HTTPException)
    """
    status_code = getattr(exc, "status_code", 500)  # For HTTPException
    detail = getattr(exc, "detail", "An unexpected error occurred") # For HTTPException
    
    if isinstance(exc, Exception):
        logger.error(f"Unexpected Error: {str(exc)}", exc_info=True)
        return JSONResponse(
            status_code=status_code,
            content={
                'code': status_code,
                'message': detail,
                'data': {}
            }
        )

# Removed init_error_handlers as FastAPI handles this directly via app.exception_handler 