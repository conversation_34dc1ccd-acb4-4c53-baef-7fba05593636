from fastapi import APIRouter, Request, Depends, HTTPException, status
from sqlalchemy.orm import Session
from auth.jwt_utils import jwt_manager
from nodes.services import create_or_update_node, get_nodes, delete_node, get_node, run_node
from utils.logger import setup_logger
from utils.error_handler import APIError
from models import get_db
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Union

logger = setup_logger('nodes.routes')
node_router = APIRouter(prefix="/api", tags=["nodes"])

class SearchContent(BaseModel):
    search_query: str
    source_type: str
    source_list: Optional[str] = None
    filter_time: Optional[str] = None
    filter_quantity: int
    is_referenced: int = Field(..., description="Whether to reference (0 or 1)", ge=0, le=1)
    selected_article_ids: List[str] # Assuming this is a list of strings (IDs)

class ArticleItem(BaseModel):
    title: str
    url: str
    content: str

class NodeCreateUpdate(BaseModel):
    agent_id: int
    content_style: str
    node_name: str
    summary_content: str
    temp_node_id: str
    node_id: Optional[int] = None # For update
    search_content: Optional[SearchContent] = None
    text_content: Optional[str] = None
    is_upload_enabled: Optional[bool] = True

@node_router.post("/node")
async def handle_create_or_update_node(
    node_data: NodeCreateUpdate,
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建或更新Node的路由处理函数
    """
    try:
        user_id = current_user['user_id']
        logger.info(f"处理Node创建/更新请求: user_id={user_id}")
        result = create_or_update_node(node_data.model_dump(exclude_unset=True), user_id, db)
        logger.info(f"Node创建/更新成功: node_id={result.get('node_id')}")
        return {"code": 200, "message": "操作成功", "data": result}
    except APIError as e:
        logger.error(f"创建/更新Node失败: {str(e)}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"创建/更新Node失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建/更新Node失败: {str(e)}"
        )

@node_router.get("/nodes")
async def handle_get_nodes(
    agent_id: int,
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取节点列表的路由处理函数
    """
    try:
        user_id = current_user['user_id']
        logger.info(f"获取节点列表请求: agent_id={agent_id}, user_id={user_id}")
        result = get_nodes(agent_id, user_id, db)
        nodes_count = len(result.get('nodes', []))
        logger.info(f"成功获取节点列表: 数量={nodes_count}")
        return {"code": 200, "message": "获取成功", "data": result}
    except APIError as e:
        logger.error(f"获取节点列表失败: {str(e)}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"获取节点列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取节点列表失败: {str(e)}"
        )

@node_router.get("/node/{node_id}")
async def handle_get_node(
    node_id: int,
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取单个节点的详细信息
    """
    try:
        user_id = current_user['user_id']
        logger.info(f"获取节点详情请求: node_id={node_id}, user_id={user_id}")
        result = get_node(node_id, user_id, db)
        logger.info(f"成功获取节点详情: node_id={node_id}")
        return {"code": 200, "message": "获取成功", "data": result}
    except APIError as e:
        logger.error(f"获取节点详情失败: {str(e)}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"获取节点详情失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取节点详情失败: {str(e)}"
        )

@node_router.delete("/node/{node_id}")
async def handle_delete_node(
    node_id: int,
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    删除指定Node的路由处理函数
    """
    try:
        user_id = current_user['user_id']
        logger.info(f"删除节点请求: node_id={node_id}, user_id={user_id}")
        result = delete_node(node_id, user_id, db)
        logger.info(f"节点删除成功: node_id={node_id}")
        return {"code": 200, "message": "删除成功", "data": result}
    except APIError as e:
        logger.error(f"删除节点失败: {str(e)}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"删除节点失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除节点失败: {str(e)}"
        )

class NodeRun(BaseModel):
    text_content: Optional[str] = None
    article_list: Optional[List[ArticleItem]] = None
    resource_id_list: Optional[List[int]] = None # Assuming resource_id_list is a list of integers

    # Custom validation to ensure at least one field is provided
    def model_post_init(self, __context: any) -> None:
        if not any([self.text_content, self.article_list, self.resource_id_list]):
            raise ValueError("At least one of text_content, article_list, or resource_id_list must be provided")


@node_router.post("/node/run")
async def handle_run_node(
    node_run_data: NodeRun,
    current_user: dict = Depends(jwt_manager.get_current_user),
    db: Session = Depends(get_db)
):
    """
    运行节点生成总结内容的路由处理函数
    """
    try:
        user_id = current_user['user_id']
        logger.info(f"处理节点运行请求: user_id={user_id}")
        
        # Convert Pydantic model to dictionary for service function
        data_to_pass = node_run_data.dict(exclude_unset=True)
        
        result = run_node(data_to_pass, user_id, db)
        logger.info("节点运行成功")
        return {"code": 200, "message": "运行成功", "data": result}
    except APIError as e:
        logger.error(f"运行节点失败: {str(e)}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"运行节点失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"运行节点失败: {str(e)}"
        )