import os

class Config:
    """
    应用配置类
    
    Attributes:
        SQLALCHEMY_DATABASE_URI (str): 数据库连接URI
        SQLALCHEMY_TRACK_MODIFICATIONS (bool): 是否跟踪数据库修改
        SECRET_KEY (str): 应用密钥
        LOG_DIR (str): 日志目录
        LOG_FILE (str): 日志文件路径
        LOG_LEVEL (str): 日志级别
    """
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://root:ffkj1314@*************:3306/aios_api'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SECRET_KEY = '9f1acfc63db48119caa3a2d437b07c76df94ea5e1d24b1c20be21610e49c9f9a'
    
    # 日志配置
    LOG_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
    LOG_FILE = os.path.join(LOG_DIR, 'app.log')
    LOG_LEVEL = 'INFO'
    
    # Redis配置
    REDIS_HOST = '*************'  # Redis服务器地址
    REDIS_PORT = 6379         # Redis端口
    REDIS_DB = 0              # Redis数据库编号
    REDIS_PASSWORD = 'a'      # Redis密码，没有则为None
    WEIXIN_CONFIG_KEY = 'weixin:crawler:config'  # 微信爬虫配置的键名