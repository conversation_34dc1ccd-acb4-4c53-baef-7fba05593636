from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from config import Config
from auth.routes import auth_router
from agents.routes import agent_router
from nodes.routes import node_router
from search.routes import search_router
from resources.routes import resource_router
from utils.error_handler import APIError, api_error_handler, http_exception_handler
from utils.logger import setup_logger
# from auth.jwt_utils import jwt_manager

# 配置日志
logger = setup_logger('app')

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有源，生产环境应该设置具体的域名
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["Content-Type", "Authorization"],
    allow_credentials=True,
)

# 初始化 SQLAlchemy
# db.init_app(app) # SQLAlchemy with Flask specific init_app, need to adapt for FastAPI

# 初始化错误处理中间件
app.exception_handler(APIError)(api_error_handler)
app.exception_handler(Exception)(http_exception_handler)

# 初始化JWT管理器
# jwt_manager.init_app(app) # Need to adapt for FastAPI

# 添加全局请求日志记录钩子
@app.middleware("http")
async def log_request_info(request: Request, call_next):
    request_logger = setup_logger('request')
    request_logger.info(f"请求路径: {request.url.path}, 请求方法: {request.method}")
    request_logger.info(f"请求头: {dict(request.headers)}")
    
    # 记录请求数据，但不记录敏感信息如密码
    if request.headers.get('content-type') == 'application/json':
        try:
            data = await request.json()
            if data and isinstance(data, dict):
                safe_data = data.copy()
                if 'password' in safe_data:
                    safe_data['password'] = '******'
                request_logger.info(f"请求JSON数据: {safe_data}")
        except Exception as e:
            request_logger.error(f"解析JSON数据失败: {e}")
    else:
        body = await request.body()
        if body:
            request_logger.info(f"请求数据: {body.decode('utf-8')[:500]}...")  # 限制日志长度
    
    response = await call_next(request)
    return response

# 注册蓝图 (改为FastAPI路由)
app.include_router(auth_router)
app.include_router(agent_router)
app.include_router(node_router)
app.include_router(search_router)
app.include_router(resource_router)
# app.register_blueprint(auth_bp, url_prefix='/api')
# app.register_blueprint(agent_bp, url_prefix='/api')
# app.register_blueprint(node_bp, url_prefix='/api')
# app.register_blueprint(search_bp, url_prefix='/api')
# app.register_blueprint(resource_bp, url_prefix='/api')

import uvicorn

if __name__ == '__main__':
    logger.info("Starting application")
    uvicorn.run(app, host="0.0.0.0", port=5000)
    # app.run(host='0.0.0.0', port=8080, debug=True, use_reloader=True)